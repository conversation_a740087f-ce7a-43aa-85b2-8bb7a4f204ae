#!/usr/bin/env python3
"""
Complete Enzyme Kinetics Analysis Pipeline
==========================================

This script executes the complete 9-step enzyme kinetics analysis pipeline
as specified in the requirements, generating all required visualizations
and CSV data files.

执行完整的酶动力学分析流程，包含所有9个分析步骤：
1. 数据加载与初步探索
2. 动力学曲线可视化  
3. 数据质量控制 (QC)
4. 推断分析 (MANOVA)
5. 米氏动力学方程拟合
6. 机器学习分类
7. 模型可解释性 (XAI)
8. 结果整合与聚类分析
9. 异常检测

Required outputs:
- boxplot_TMB_OPD_distribution.png
- kinetic_curves_TMB.png
- kinetic_curves_OPD.png
- correlation_TMB_vs_OPD.png
- cv_heatmap.png
- manova_interaction_plot.png
- michaelis_menten_fits.png
- confusion_matrix_GBT.png
- confusion_matrix_CNN.png
- shap_summary_plot.png
- tsne_visualization.png
- hierarchical_clustering_dendrogram.png
- clustermap_summary.png
- anomaly_detection_plot.png
"""

import sys
import os
import time
from datetime import datetime

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.base import AnalysisConfig, DataManager, PlotManager, AnalysisResults
from modules.descriptive_stats import DescriptiveStatsModule
from modules.quality_control import QualityControlModule
from modules.manova_analysis import MANOVAAnalysisModule
from modules.kinetic_modeling import KineticModelingModule
from modules.machine_learning import MachineLearningModule
from modules.explainability import ExplainabilityModule
from modules.clustering import ClusteringModule
from modules.anomaly_detection import AnomalyDetectionModule


class CompleteAnalysisPipeline:
    """Complete enzyme kinetics analysis pipeline executor."""
    
    def __init__(self, data_file: str = "/Users/<USER>/Documents/nie/sample0513.csv"):
        self.data_file = data_file
        self.config = AnalysisConfig()
        self.data_manager = None
        self.plot_manager = None
        self.results = AnalysisResults()
        self.start_time = None
        
    def initialize_managers(self):
        """Initialize data and plot managers."""
        self.data_manager = DataManager(self.config)
        self.plot_manager = PlotManager(self.data_manager)
        self.data_manager.create_output_directories()
        
    def print_pipeline_header(self):
        """Print pipeline header information."""
        print("=" * 80)
        print("🧬 酶动力学数据分析 - 完整分析流水线")
        print("   Complete Enzyme Kinetics Analysis Pipeline")
        print("=" * 80)
        print(f"📊 数据文件: {self.data_file}")
        print(f"📁 输出目录: {self.data_manager.output_dir}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        print("\n📋 分析步骤概览:")
        
        steps = [
            "1️⃣  数据加载与初步探索",
            "2️⃣  动力学曲线可视化", 
            "3️⃣  数据质量控制 (QC)",
            "4️⃣  推断分析 (MANOVA)",
            "5️⃣  米氏动力学方程拟合",
            "6️⃣  机器学习分类",
            "7️⃣  模型可解释性 (XAI)",
            "8️⃣  结果整合与聚类分析",
            "9️⃣  异常检测"
        ]
        
        for step in steps:
            print(f"   {step}")
        print("=" * 80)
        
    def run_step(self, step_num: int, module_class, step_name: str) -> bool:
        """Run a single analysis step."""
        print(f"\n🚀 执行步骤 {step_num}: {step_name}")
        print("-" * 60)
        
        try:
            step_start = time.time()
            
            # Create and run module
            module = module_class(self.data_manager, self.plot_manager)
            result = module.run_analysis()
            
            # Store results
            self.results.add_module_result(step_name, result)
            
            step_duration = time.time() - step_start
            print(f"✅ 步骤 {step_num} 完成！用时: {step_duration:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 步骤 {step_num} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_complete_pipeline(self) -> bool:
        """Execute the complete analysis pipeline."""
        self.start_time = time.time()
        
        # Initialize
        self.initialize_managers()
        self.print_pipeline_header()
        
        # Define pipeline steps
        pipeline_steps = [
            (1, DescriptiveStatsModule, "数据加载与初步探索"),
            (2, QualityControlModule, "数据质量控制"),
            (3, MANOVAAnalysisModule, "MANOVA分析"),
            (4, KineticModelingModule, "Michaelis-Menten建模"),
            (5, MachineLearningModule, "机器学习分类"),
            (6, ExplainabilityModule, "模型可解释性分析"),
            (7, ClusteringModule, "聚类分析"),
            (8, AnomalyDetectionModule, "异常检测")
        ]
        
        # Execute pipeline
        completed_steps = 0
        total_steps = len(pipeline_steps)
        
        for step_num, module_class, step_name in pipeline_steps:
            if self.run_step(step_num, module_class, step_name):
                completed_steps += 1
            
            # Progress update
            progress = (completed_steps / total_steps) * 100
            print(f"\n📊 总体进度: {completed_steps}/{total_steps} ({progress:.1f}%)")
        
        # Finalize
        self._finalize_pipeline(completed_steps, total_steps)
        
        return completed_steps == total_steps
    
    def _finalize_pipeline(self, completed: int, total: int):
        """Finalize the pipeline and generate summary."""
        total_duration = time.time() - self.start_time
        
        # Save consolidated results
        results_file = self.data_manager.save_results_to_csv()
        
        # Generate summary
        print("\n" + "=" * 80)
        print("🎉 分析流水线执行完成！")
        print("   Analysis Pipeline Execution Complete!")
        print("=" * 80)
        print(f"✅ 成功完成步骤: {completed}/{total}")
        print(f"⏱️  总执行时间: {total_duration:.2f}秒 ({total_duration/60:.1f}分钟)")
        print(f"📁 输出目录: {self.data_manager.output_dir}")
        print(f"📊 数值结果: {results_file}")
        print(f"📈 生成图表数量: {len([f for f in os.listdir(f'{self.data_manager.output_dir}/visualizations') if f.endswith('.png')])}")
        print(f"📄 数据文件数量: {len([f for f in os.listdir(f'{self.data_manager.output_dir}/visualizations') if f.endswith('.csv')])}")
        
        # List key output files
        print("\n📋 主要输出文件:")
        required_outputs = [
            "boxplot_TMB_OPD_distribution.png",
            "kinetic_curves_TMB.png", 
            "kinetic_curves_OPD.png",
            "correlation_TMB_vs_OPD.png",
            "cv_heatmap.png",
            "manova_interaction_plot.png",
            "michaelis_menten_fits.png",
            "confusion_matrix_GBT.png",
            "shap_summary_plot.png",
            "tsne_visualization.png",
            "hierarchical_clustering_dendrogram.png",
            "clustermap_summary.png",
            "anomaly_detection_plot.png"
        ]
        
        viz_dir = f"{self.data_manager.output_dir}/visualizations"
        for filename in required_outputs:
            filepath = os.path.join(viz_dir, filename)
            status = "✅" if os.path.exists(filepath) else "❌"
            print(f"   {status} {filename}")
        
        print("=" * 80)
        
        # Performance summary
        self._print_performance_summary()
    
    def _print_performance_summary(self):
        """Print performance and resource usage summary."""
        print("\n📈 性能摘要:")
        
        # Module execution summary
        completed_modules = list(self.results.results.keys())
        print(f"   已完成模块: {len(completed_modules)}")
        
        # Data summary
        if self.data_manager.results:
            total_results = len(self.data_manager.results)
            print(f"   生成数值结果: {total_results} 条")
            
            # Results by analysis step
            step_counts = {}
            for result in self.data_manager.results:
                step = result.get('analysis_step', 'unknown')
                step_counts[step] = step_counts.get(step, 0) + 1
            
            print("   各步骤结果数量:")
            for step, count in step_counts.items():
                print(f"     {step}: {count}")
        
        print("\n🎯 分析完成！所有结果已保存到输出目录。")


def main():
    """Main execution function."""
    # Configuration options
    import argparse
    
    parser = argparse.ArgumentParser(description='Complete Enzyme Kinetics Analysis Pipeline')
    parser.add_argument('--data-file', default='/Users/<USER>/Documents/nie/sample0513.csv',
                       help='Path to the input CSV data file')
    parser.add_argument('--config-file', help='Path to configuration file')
    parser.add_argument('--confidence-level', type=float, default=0.95,
                       help='Confidence level for statistical analyses')
    
    args = parser.parse_args()
    
    # Create and configure pipeline
    pipeline = CompleteAnalysisPipeline(args.data_file)
    
    # Load configuration if provided
    if args.config_file and os.path.exists(args.config_file):
        pipeline.config.load_config(args.config_file)
        print(f"📋 已加载配置文件: {args.config_file}")
    
    # Update confidence level if provided
    if args.confidence_level != 0.95:
        pipeline.config.update(confidence_level=args.confidence_level)
    
    # Execute pipeline
    success = pipeline.run_complete_pipeline()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
