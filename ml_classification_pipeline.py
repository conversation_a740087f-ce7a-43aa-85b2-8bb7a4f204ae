#!/usr/bin/env python3
"""
机器学习分类管道 - 小样本修正版
基于酶反应数据进行样本分类的完整机器学习流程
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import LeaveOneOut
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC, LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (accuracy_score, classification_report, 
                           confusion_matrix, roc_curve, auc)
from sklearn.multiclass import OneVsRestClassifier
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False
    print("警告: XGBoost未安装，将跳过XGBoost模型")

from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib参数
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12
plt.rcParams['figure.dpi'] = 300

class EnzymeMLPipeline:
    """酶反应数据机器学习分类管道"""
    
    def __init__(self, data_path):
        """初始化管道"""
        self.data_path = data_path
        self.raw_data = None
        self.feature_matrix = None
        self.labels = None
        self.models = {}
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.raw_data = pd.read_csv(self.data_path)
        # 清理浓度列，移除μM单位
        self.raw_data['concentration'] = self.raw_data['concentration'].str.replace(' μM', '').astype(float)
        print(f"数据加载完成: {self.raw_data.shape}")
        return self.raw_data
    
    def michaelis_menten(self, x, vmax, km):
        """Michaelis-Menten动力学方程"""
        return (vmax * x) / (km + x)
    
    def fit_kinetics(self, concentrations, responses):
        """拟合动力学参数"""
        try:
            # 初始参数估计
            vmax_init = np.max(responses) * 1.2
            km_init = concentrations[np.argmax(np.diff(responses))]
            
            popt, _ = curve_fit(
                self.michaelis_menten, 
                concentrations, 
                responses,
                p0=[vmax_init, km_init],
                bounds=([0, 0], [np.inf, np.inf]),
                maxfev=1000
            )
            return popt[0], popt[1]  # vmax, km
        except:
            return np.nan, np.nan
    
    def create_features(self):
        """特征工程 - 创建综合特征向量"""
        print("开始特征工程...")

        if self.raw_data is None:
            raise ValueError("请先运行load_data()方法")

        # 获取所有平行样
        parallels = self.raw_data['Parallel'].unique()
        features_list = []
        labels_list = []
        
        for parallel in parallels:
            parallel_data = self.raw_data[self.raw_data['Parallel'] == parallel].copy()
            parallel_data = parallel_data.sort_values('concentration')
            
            sample_label = parallel_data['sample'].iloc[0]
            concentrations = parallel_data['concentration'].values
            tmb_values = parallel_data['TMB'].values
            opd_values = parallel_data['OPD'].values
            
            # 1. 基础特征 - 宽格式转换
            base_features = {}
            for i, conc in enumerate(concentrations):
                base_features[f'TMB_{conc}μM'] = tmb_values[i]
                base_features[f'OPD_{conc}μM'] = opd_values[i]
            
            # 2. 比率特征
            ratios = tmb_values / (opd_values + 1e-8)  # 避免除零
            base_features['TMB_OPD_ratio_mean'] = np.mean(ratios)
            base_features['TMB_OPD_ratio_std'] = np.std(ratios)
            
            # 3. 聚合统计特征
            for substrate, values in [('TMB', tmb_values), ('OPD', opd_values)]:
                base_features[f'{substrate}_mean'] = np.mean(values)
                base_features[f'{substrate}_std'] = np.std(values)
                base_features[f'{substrate}_median'] = np.median(values)
                base_features[f'{substrate}_min'] = np.min(values)
                base_features[f'{substrate}_max'] = np.max(values)
            
            # 4. 动力学拟合特征
            vmax_tmb, km_tmb = self.fit_kinetics(concentrations, tmb_values)
            vmax_opd, km_opd = self.fit_kinetics(concentrations, opd_values)
            
            base_features['Vmax_TMB'] = vmax_tmb
            base_features['Km_TMB'] = km_tmb
            base_features['Vmax_OPD'] = vmax_opd
            base_features['Km_OPD'] = km_opd
            
            # 5. 梯度特征
            tmb_gradients = np.diff(tmb_values) / np.diff(concentrations)
            opd_gradients = np.diff(opd_values) / np.diff(concentrations)
            
            base_features['TMB_avg_gradient'] = np.mean(tmb_gradients)
            base_features['OPD_avg_gradient'] = np.mean(opd_gradients)
            
            features_list.append(base_features)
            labels_list.append(sample_label)
        
        # 转换为DataFrame
        self.feature_matrix = pd.DataFrame(features_list)
        self.labels = np.array(labels_list)
        
        # 处理NaN值
        self.feature_matrix = self.feature_matrix.fillna(self.feature_matrix.mean())
        
        print(f"特征工程完成: {self.feature_matrix.shape}")
        print(f"特征数量: {self.feature_matrix.shape[1]}")
        print(f"样本分布: {pd.Series(self.labels).value_counts().to_dict()}")
        
        return self.feature_matrix, self.labels
    
    def prepare_models(self):
        """准备模型"""
        print("准备模型...")

        self.models = {
            'Logistic_Regression': LogisticRegression(
                random_state=42,
                max_iter=1000,
                C=0.1,  # 强正则化
                multi_class='ovr'
            ),
            'Linear_SVM': LinearSVC(
                random_state=42,
                C=0.1,  # 强正则化
                max_iter=2000
            ),
            'RBF_SVM': SVC(
                random_state=42,
                C=0.1,
                kernel='rbf',
                probability=True  # 用于ROC计算
            ),
            'Random_Forest': RandomForestClassifier(
                random_state=42,
                n_estimators=20,  # 少量估计器
                max_depth=3,      # 限制深度
                min_samples_split=5
            )
        }

        # 只有在XGBoost可用时才添加
        if HAS_XGBOOST:
            self.models['XGBoost'] = xgb.XGBClassifier(
                random_state=42,
                n_estimators=20,
                max_depth=3,
                learning_rate=0.1,
                subsample=0.8,
                objective='multi:softprob'
            )
        
        print(f"准备了 {len(self.models)} 个模型")
        return self.models

    def evaluate_models(self):
        """使用留一法交叉验证评估模型"""
        print("开始模型评估...")

        if self.feature_matrix is None or self.labels is None:
            raise ValueError("请先运行create_features()方法")

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.feature_matrix.values)

        # 标签编码
        le = LabelEncoder()
        y_encoded = le.fit_transform(self.labels)

        # 留一法交叉验证
        loo = LeaveOneOut()

        for model_name, model in self.models.items():
            print(f"评估模型: {model_name}")

            y_true = []
            y_pred = []
            y_proba = []

            for train_idx, test_idx in loo.split(X_scaled):
                X_train, X_test = X_scaled[train_idx], X_scaled[test_idx]
                y_train, y_test = y_encoded[train_idx], y_encoded[test_idx]

                # 训练模型
                if model_name == 'Linear_SVM':
                    # LinearSVC不支持predict_proba
                    model_fitted = model.fit(X_train, y_train)
                    pred = model_fitted.predict(X_test)
                    # 使用decision_function作为概率替代
                    decision = model_fitted.decision_function(X_test)
                    if len(le.classes_) == 2:
                        proba = np.column_stack([1-decision, decision])
                    else:
                        # 多类情况下的处理
                        proba = np.zeros((1, len(le.classes_)))
                        proba[0, pred[0]] = 1.0
                else:
                    model_fitted = model.fit(X_train, y_train)
                    pred = model_fitted.predict(X_test)
                    proba = model_fitted.predict_proba(X_test)

                y_true.extend(y_test)
                y_pred.extend(pred)
                y_proba.append(proba[0])

            # 保存结果
            self.results[model_name] = {
                'y_true': np.array(y_true),
                'y_pred': np.array(y_pred),
                'y_proba': np.array(y_proba),
                'accuracy': accuracy_score(y_true, y_pred),
                'labels': le.classes_
            }

            print(f"{model_name} 准确率: {self.results[model_name]['accuracy']:.4f}")

        return self.results

    def generate_confusion_matrices(self):
        """生成混淆矩阵"""
        print("生成混淆矩阵...")

        for model_name, result in self.results.items():
            y_true = result['y_true']
            y_pred = result['y_pred']
            labels = result['labels']

            # 原始混淆矩阵
            cm_raw = confusion_matrix(y_true, y_pred)
            cm_normalized = confusion_matrix(y_true, y_pred, normalize='true')

            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

            # 原始混淆矩阵
            sns.heatmap(cm_raw, annot=True, fmt='d', cmap='Blues',
                       xticklabels=labels, yticklabels=labels, ax=ax1)
            ax1.set_xlabel('预测标签')
            ax1.set_ylabel('真实标签')

            # 归一化混淆矩阵
            sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                       xticklabels=labels, yticklabels=labels, ax=ax2)
            ax2.set_xlabel('预测标签')
            ax2.set_ylabel('真实标签')

            plt.tight_layout()
            plt.savefig(f'confusion_matrix_{model_name}.png', dpi=300, bbox_inches='tight')
            plt.close()

            # 保存CSV
            cm_df = pd.DataFrame(cm_raw, index=labels, columns=labels)
            cm_df.to_csv(f'confusion_matrix_raw_{model_name}.csv')

            cm_norm_df = pd.DataFrame(cm_normalized, index=labels, columns=labels)
            cm_norm_df.to_csv(f'confusion_matrix_normalized_{model_name}.csv')

    def generate_classification_reports(self):
        """生成分类报告"""
        print("生成分类报告...")

        for model_name, result in self.results.items():
            y_true = result['y_true']
            y_pred = result['y_pred']
            labels = result['labels']

            # 生成分类报告
            report = classification_report(
                y_true, y_pred,
                target_names=labels,
                output_dict=True
            )

            # 转换为DataFrame并保存
            report_df = pd.DataFrame(report).transpose()
            report_df.to_csv(f'classification_report_{model_name}.csv')

            print(f"{model_name} 分类报告已保存")

    def generate_roc_curves(self):
        """生成ROC曲线"""
        print("生成ROC曲线...")

        for model_name, result in self.results.items():
            if model_name == 'Linear_SVM':
                continue  # 跳过LinearSVM，因为概率不准确

            y_true = result['y_true']
            y_proba = result['y_proba']
            labels = result['labels']

            # 创建图形
            plt.figure(figsize=(8, 6))

            # 计算每个类别的ROC
            auc_scores = []
            for i, class_label in enumerate(labels):
                # 二分类：当前类 vs 其他类
                y_binary = (y_true == i).astype(int)
                y_score = y_proba[:, i]

                fpr, tpr, _ = roc_curve(y_binary, y_score)
                roc_auc = auc(fpr, tpr)
                auc_scores.append(roc_auc)

                plt.plot(fpr, tpr, linewidth=2,
                        label=f'类别 {class_label} (AUC = {roc_auc:.3f})')

                # 保存ROC数据
                roc_data = pd.DataFrame({
                    'FPR': fpr,
                    'TPR': tpr,
                    'Class': class_label,
                    'AUC': roc_auc
                })
                roc_data.to_csv(f'roc_curve_{model_name}_class_{class_label}.csv', index=False)

            # 计算宏平均AUC
            macro_auc = np.mean(auc_scores)

            plt.plot([0, 1], [0, 1], 'k--', linewidth=1)
            plt.xlim([0.0, 1.0])
            plt.ylim([0.0, 1.05])
            plt.xlabel('假正率 (FPR)')
            plt.ylabel('真正率 (TPR)')
            plt.legend(loc="lower right")
            plt.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(f'roc_curves_{model_name}.png', dpi=300, bbox_inches='tight')
            plt.close()

            # 保存AUC汇总
            auc_summary = pd.DataFrame({
                'Class': labels,
                'AUC': auc_scores
            })
            auc_summary.loc[len(auc_summary)] = ['Macro_Average', macro_auc]
            auc_summary.to_csv(f'auc_summary_{model_name}.csv', index=False)

            print(f"{model_name} 宏平均AUC: {macro_auc:.4f}")

    def generate_feature_importance(self):
        """生成特征重要性分析"""
        print("生成特征重要性分析...")

        if self.feature_matrix is None or self.labels is None:
            raise ValueError("请先运行create_features()方法")

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.feature_matrix.values)

        # 标签编码
        le = LabelEncoder()
        y_encoded = le.fit_transform(self.labels)

        # 只对支持特征重要性的模型进行分析
        importance_models = ['Random_Forest', 'XGBoost']

        for model_name in importance_models:
            if model_name in self.models:
                model = self.models[model_name]
                model.fit(X_scaled, y_encoded)

                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    feature_names = self.feature_matrix.columns

                    # 创建特征重要性DataFrame
                    importance_df = pd.DataFrame({
                        'Feature': feature_names,
                        'Importance': importances
                    }).sort_values('Importance', ascending=False)

                    # 保存CSV
                    importance_df.to_csv(f'feature_importance_{model_name}.csv', index=False)

                    # 绘制特征重要性图
                    plt.figure(figsize=(8, 6))
                    top_features = importance_df.head(15)  # 显示前15个重要特征

                    plt.barh(range(len(top_features)), top_features['Importance'])
                    plt.yticks(range(len(top_features)), top_features['Feature'].tolist())
                    plt.xlabel('特征重要性')
                    plt.gca().invert_yaxis()
                    plt.tight_layout()

                    plt.savefig(f'feature_importance_{model_name}.png', dpi=300, bbox_inches='tight')
                    plt.close()

                    print(f"{model_name} 特征重要性分析完成")

    def generate_summary_report(self):
        """生成汇总报告"""
        print("生成汇总报告...")

        summary_data = []
        for model_name, result in self.results.items():
            summary_data.append({
                'Model': model_name,
                'Accuracy': result['accuracy'],
                'Sample_Size': len(result['y_true']),
                'Classes': len(result['labels'])
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Accuracy', ascending=False)
        summary_df.to_csv('model_performance_summary.csv', index=False)

        # 绘制模型性能对比图
        plt.figure(figsize=(8, 6))
        plt.bar(summary_df['Model'], summary_df['Accuracy'])
        plt.xlabel('模型')
        plt.ylabel('准确率')
        plt.xticks(rotation=45)
        plt.ylim(0, 1)

        # 添加数值标签
        for i, v in enumerate(summary_df['Accuracy']):
            plt.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("汇总报告生成完成")
        return summary_df

    def run_complete_pipeline(self):
        """运行完整的机器学习管道"""
        print("=" * 60)
        print("开始运行完整的机器学习分类管道")
        print("=" * 60)

        # 1. 加载数据
        self.load_data()

        # 2. 特征工程
        self.create_features()

        # 3. 准备模型
        self.prepare_models()

        # 4. 模型评估
        self.evaluate_models()

        # 5. 生成混淆矩阵
        self.generate_confusion_matrices()

        # 6. 生成分类报告
        self.generate_classification_reports()

        # 7. 生成ROC曲线
        self.generate_roc_curves()

        # 8. 生成特征重要性分析
        self.generate_feature_importance()

        # 9. 生成汇总报告
        summary = self.generate_summary_report()

        print("=" * 60)
        print("机器学习管道执行完成!")
        print("=" * 60)
        print("\n最佳模型性能:")
        print(summary.head())

        return summary


def main():
    """主函数"""
    # 创建管道实例
    pipeline = EnzymeMLPipeline('sample0513.csv')

    # 运行完整管道
    results = pipeline.run_complete_pipeline()

    print("\n所有结果文件已生成:")
    print("- 混淆矩阵: confusion_matrix_*.png, confusion_matrix_*.csv")
    print("- 分类报告: classification_report_*.csv")
    print("- ROC曲线: roc_curves_*.png, roc_curve_*.csv, auc_summary_*.csv")
    print("- 特征重要性: feature_importance_*.png, feature_importance_*.csv")
    print("- 模型性能对比: model_performance_comparison.png, model_performance_summary.csv")


if __name__ == "__main__":
    main()
