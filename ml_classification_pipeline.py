

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import LeaveOneOut
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC, LinearSVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (accuracy_score, classification_report,
                           confusion_matrix, roc_curve, auc)
from sklearn.multiclass import OneVsRestClassifier
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False
    print("Warning: XGBoost not installed, will skip XGBoost model")

from scipy.optimize import curve_fit
import warnings
import os
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置matplotlib参数
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12
plt.rcParams['figure.dpi'] = 300

class EnzymeMLPipeline:
    """Enzyme reaction data machine learning classification pipeline"""

    def __init__(self, data_path):
        """Initialize pipeline"""
        self.data_path = data_path
        self.raw_data = None
        self.feature_matrix = None
        self.labels = None
        self.models = {}
        self.results = {}

        # Create output directory with timestamp
        timestamp = datetime.now().strftime("%y%m%d%H%M")
        self.output_dir = f"outputs/{timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"Output directory created: {self.output_dir}")
        
    def load_data(self):
        """Load data"""
        print("Loading data...")
        self.raw_data = pd.read_csv(self.data_path)
        # Clean concentration column, remove μM unit
        self.raw_data['concentration'] = self.raw_data['concentration'].str.replace(' μM', '').astype(float)
        print(f"Data loaded: {self.raw_data.shape}")
        return self.raw_data
    
    def michaelis_menten(self, x, vmax, km):
        """Michaelis-Menten动力学方程"""
        return (vmax * x) / (km + x)
    
    def fit_kinetics(self, concentrations, responses):
        """拟合动力学参数"""
        try:
            # 初始参数估计
            vmax_init = np.max(responses) * 1.2
            km_init = concentrations[np.argmax(np.diff(responses))]
            
            popt, _ = curve_fit(
                self.michaelis_menten, 
                concentrations, 
                responses,
                p0=[vmax_init, km_init],
                bounds=([0, 0], [np.inf, np.inf]),
                maxfev=1000
            )
            return popt[0], popt[1]  # vmax, km
        except:
            return np.nan, np.nan
    
    def create_features(self):
        """Feature engineering - Create comprehensive feature vectors"""
        print("Starting feature engineering...")

        if self.raw_data is None:
            raise ValueError("Please run load_data() method first")

        # Get all parallel samples
        parallels = self.raw_data['Parallel'].unique()
        features_list = []
        labels_list = []
        
        for parallel in parallels:
            parallel_data = self.raw_data[self.raw_data['Parallel'] == parallel].copy()
            parallel_data = parallel_data.sort_values('concentration')
            
            sample_label = parallel_data['sample'].iloc[0]
            concentrations = parallel_data['concentration'].values
            tmb_values = parallel_data['TMB'].values
            opd_values = parallel_data['OPD'].values
            
            # 1. 基础特征 - 宽格式转换
            base_features = {}
            for i, conc in enumerate(concentrations):
                base_features[f'TMB_{conc}μM'] = tmb_values[i]
                base_features[f'OPD_{conc}μM'] = opd_values[i]
            
            # 2. 比率特征
            ratios = tmb_values / (opd_values + 1e-8)  # 避免除零
            base_features['TMB_OPD_ratio_mean'] = np.mean(ratios)
            base_features['TMB_OPD_ratio_std'] = np.std(ratios)
            
            # 3. 聚合统计特征
            for substrate, values in [('TMB', tmb_values), ('OPD', opd_values)]:
                base_features[f'{substrate}_mean'] = np.mean(values)
                base_features[f'{substrate}_std'] = np.std(values)
                base_features[f'{substrate}_median'] = np.median(values)
                base_features[f'{substrate}_min'] = np.min(values)
                base_features[f'{substrate}_max'] = np.max(values)
            
            # 4. 动力学拟合特征
            vmax_tmb, km_tmb = self.fit_kinetics(concentrations, tmb_values)
            vmax_opd, km_opd = self.fit_kinetics(concentrations, opd_values)
            
            base_features['Vmax_TMB'] = vmax_tmb
            base_features['Km_TMB'] = km_tmb
            base_features['Vmax_OPD'] = vmax_opd
            base_features['Km_OPD'] = km_opd
            
            # 5. 梯度特征
            tmb_gradients = np.diff(tmb_values) / np.diff(concentrations)
            opd_gradients = np.diff(opd_values) / np.diff(concentrations)
            
            base_features['TMB_avg_gradient'] = np.mean(tmb_gradients)
            base_features['OPD_avg_gradient'] = np.mean(opd_gradients)
            
            features_list.append(base_features)
            labels_list.append(sample_label)
        
        # 转换为DataFrame
        self.feature_matrix = pd.DataFrame(features_list)
        self.labels = np.array(labels_list)
        
        # 处理NaN值
        self.feature_matrix = self.feature_matrix.fillna(self.feature_matrix.mean())
        
        print(f"Feature engineering completed: {self.feature_matrix.shape}")
        print(f"Number of features: {self.feature_matrix.shape[1]}")
        print(f"Sample distribution: {pd.Series(self.labels).value_counts().to_dict()}")
        
        return self.feature_matrix, self.labels
    
    def prepare_models(self):
        """Prepare models"""
        print("Preparing models...")

        self.models = {
            'Logistic_Regression': LogisticRegression(
                random_state=42,
                max_iter=1000,
                C=0.1,  # 强正则化
                multi_class='ovr'
            ),
            'Linear_SVM': LinearSVC(
                random_state=42,
                C=0.1,  # 强正则化
                max_iter=2000
            ),
            'RBF_SVM': SVC(
                random_state=42,
                C=0.1,
                kernel='rbf',
                probability=True  # 用于ROC计算
            ),
            'Random_Forest': RandomForestClassifier(
                random_state=42,
                n_estimators=20,  # 少量估计器
                max_depth=3,      # 限制深度
                min_samples_split=5
            )
        }

        # 只有在XGBoost可用时才添加
        if HAS_XGBOOST:
            self.models['XGBoost'] = xgb.XGBClassifier(
                random_state=42,
                n_estimators=20,
                max_depth=3,
                learning_rate=0.1,
                subsample=0.8,
                objective='multi:softprob'
            )
        
        print(f"Prepared {len(self.models)} models")
        return self.models

    def evaluate_models(self):
        """Evaluate models using Leave-One-Out Cross-Validation"""
        print("Starting model evaluation...")

        if self.feature_matrix is None or self.labels is None:
            raise ValueError("请先运行create_features()方法")

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.feature_matrix.values)

        # 标签编码
        le = LabelEncoder()
        y_encoded = le.fit_transform(self.labels)

        # 留一法交叉验证
        loo = LeaveOneOut()

        for model_name, model in self.models.items():
            print(f"Evaluating model: {model_name}")

            y_true = []
            y_pred = []
            y_proba = []

            for train_idx, test_idx in loo.split(X_scaled):
                X_train, X_test = X_scaled[train_idx], X_scaled[test_idx]
                y_train, y_test = y_encoded[train_idx], y_encoded[test_idx]

                # Train model
                if model_name == 'Linear_SVM':
                    # LinearSVC doesn't support predict_proba
                    model_fitted = model.fit(X_train, y_train)
                    pred = model_fitted.predict(X_test)
                    # Use decision_function as probability substitute
                    decision = model_fitted.decision_function(X_test)
                    if len(le.classes_) == 2:
                        proba = np.column_stack([1-decision, decision])
                    else:
                        # Handle multiclass case
                        proba = np.zeros((1, len(le.classes_)))
                        proba[0, pred[0]] = 1.0
                else:
                    model_fitted = model.fit(X_train, y_train)
                    pred = model_fitted.predict(X_test)
                    proba = model_fitted.predict_proba(X_test)

                y_true.extend(y_test)
                y_pred.extend(pred)
                y_proba.append(proba[0])

            # Save results
            self.results[model_name] = {
                'y_true': np.array(y_true),
                'y_pred': np.array(y_pred),
                'y_proba': np.array(y_proba),
                'accuracy': accuracy_score(y_true, y_pred),
                'labels': le.classes_
            }

            print(f"{model_name} accuracy: {self.results[model_name]['accuracy']:.4f}")

        return self.results

    def generate_confusion_matrices(self):
        """Generate confusion matrices"""
        print("Generating confusion matrices...")

        for model_name, result in self.results.items():
            y_true = result['y_true']
            y_pred = result['y_pred']
            labels = result['labels']

            # Raw confusion matrix
            cm_raw = confusion_matrix(y_true, y_pred)
            cm_normalized = confusion_matrix(y_true, y_pred, normalize='true')

            # Create figure
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

            # Raw confusion matrix
            sns.heatmap(cm_raw, annot=True, fmt='d', cmap='Blues',
                       xticklabels=labels, yticklabels=labels, ax=ax1)
            ax1.set_xlabel('Predicted Label')
            ax1.set_ylabel('True Label')

            # Normalized confusion matrix
            sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                       xticklabels=labels, yticklabels=labels, ax=ax2)
            ax2.set_xlabel('Predicted Label')
            ax2.set_ylabel('True Label')

            plt.tight_layout()

            # Save figure
            fig_path = os.path.join(self.output_dir, f'confusion_matrix_{model_name}.png')
            plt.savefig(fig_path, dpi=300, bbox_inches='tight')
            plt.close()

            # Save corresponding CSV data for the figure
            cm_combined_df = pd.DataFrame({
                'True_Label': np.repeat(labels, len(labels)),
                'Predicted_Label': np.tile(labels, len(labels)),
                'Raw_Count': cm_raw.flatten(),
                'Normalized_Value': cm_normalized.flatten()
            })
            csv_path = os.path.join(self.output_dir, f'confusion_matrix_{model_name}.csv')
            cm_combined_df.to_csv(csv_path, index=False)

    def generate_classification_reports(self):
        """Generate classification reports"""
        print("Generating classification reports...")

        for model_name, result in self.results.items():
            y_true = result['y_true']
            y_pred = result['y_pred']
            labels = result['labels']

            # Generate classification report
            report = classification_report(
                y_true, y_pred,
                target_names=labels,
                output_dict=True
            )

            # Convert to DataFrame and save
            report_df = pd.DataFrame(report).transpose()
            csv_path = os.path.join(self.output_dir, f'classification_report_{model_name}.csv')
            report_df.to_csv(csv_path)

            print(f"{model_name} classification report saved")

    def generate_roc_curves(self):
        """Generate ROC curves"""
        print("Generating ROC curves...")

        for model_name, result in self.results.items():
            if model_name == 'Linear_SVM':
                continue  # Skip LinearSVM due to inaccurate probabilities

            y_true = result['y_true']
            y_proba = result['y_proba']
            labels = result['labels']

            # Create figure
            plt.figure(figsize=(8, 6))

            # Calculate ROC for each class
            auc_scores = []
            all_roc_data = []

            for i, class_label in enumerate(labels):
                # Binary classification: current class vs others
                y_binary = (y_true == i).astype(int)
                y_score = y_proba[:, i]

                fpr, tpr, _ = roc_curve(y_binary, y_score)
                roc_auc = auc(fpr, tpr)
                auc_scores.append(roc_auc)

                plt.plot(fpr, tpr, linewidth=2,
                        label=f'Class {class_label} (AUC = {roc_auc:.3f})')

                # Collect ROC data for combined CSV
                for j in range(len(fpr)):
                    all_roc_data.append({
                        'Class': class_label,
                        'FPR': fpr[j],
                        'TPR': tpr[j],
                        'AUC': roc_auc
                    })

            # Calculate macro average AUC
            macro_auc = np.mean(auc_scores)

            plt.plot([0, 1], [0, 1], 'k--', linewidth=1)
            plt.xlim([0.0, 1.0])
            plt.ylim([0.0, 1.05])
            plt.xlabel('False Positive Rate (FPR)')
            plt.ylabel('True Positive Rate (TPR)')
            plt.legend(loc="lower right")
            plt.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save figure
            fig_path = os.path.join(self.output_dir, f'roc_curves_{model_name}.png')
            plt.savefig(fig_path, dpi=300, bbox_inches='tight')
            plt.close()

            # Save corresponding CSV data for the figure
            roc_df = pd.DataFrame(all_roc_data)
            csv_path = os.path.join(self.output_dir, f'roc_curves_{model_name}.csv')
            roc_df.to_csv(csv_path, index=False)

            # Save AUC summary
            auc_summary = pd.DataFrame({
                'Class': list(labels) + ['Macro_Average'],
                'AUC': auc_scores + [macro_auc]
            })
            auc_path = os.path.join(self.output_dir, f'auc_summary_{model_name}.csv')
            auc_summary.to_csv(auc_path, index=False)

            print(f"{model_name} Macro Average AUC: {macro_auc:.4f}")

    def generate_feature_importance(self):
        """Generate feature importance analysis"""
        print("Generating feature importance analysis...")

        if self.feature_matrix is None or self.labels is None:
            raise ValueError("Please run create_features() method first")

        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.feature_matrix.values)

        # Label encoding
        le = LabelEncoder()
        y_encoded = le.fit_transform(self.labels)

        # Only analyze models that support feature importance
        importance_models = ['Random_Forest', 'XGBoost']

        for model_name in importance_models:
            if model_name in self.models:
                model = self.models[model_name]
                model.fit(X_scaled, y_encoded)

                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    feature_names = self.feature_matrix.columns

                    # Create feature importance DataFrame
                    importance_df = pd.DataFrame({
                        'Feature': feature_names,
                        'Importance': importances
                    }).sort_values('Importance', ascending=False)

                    # Plot feature importance
                    plt.figure(figsize=(8, 6))
                    top_features = importance_df.head(15)  # Show top 15 important features

                    plt.barh(range(len(top_features)), top_features['Importance'])
                    plt.yticks(range(len(top_features)), top_features['Feature'].tolist())
                    plt.xlabel('Feature Importance')
                    plt.gca().invert_yaxis()
                    plt.tight_layout()

                    # Save figure
                    fig_path = os.path.join(self.output_dir, f'feature_importance_{model_name}.png')
                    plt.savefig(fig_path, dpi=300, bbox_inches='tight')
                    plt.close()

                    # Save corresponding CSV data for the figure
                    csv_path = os.path.join(self.output_dir, f'feature_importance_{model_name}.csv')
                    importance_df.to_csv(csv_path, index=False)

                    print(f"{model_name} feature importance analysis completed")

    def generate_summary_report(self):
        """Generate summary report"""
        print("Generating summary report...")

        summary_data = []
        for model_name, result in self.results.items():
            summary_data.append({
                'Model': model_name,
                'Accuracy': result['accuracy'],
                'Sample_Size': len(result['y_true']),
                'Classes': len(result['labels'])
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Accuracy', ascending=False)

        # Plot model performance comparison
        plt.figure(figsize=(8, 6))
        plt.bar(summary_df['Model'], summary_df['Accuracy'])
        plt.xlabel('Model')
        plt.ylabel('Accuracy')
        plt.xticks(rotation=45)
        plt.ylim(0, 1)

        # Add value labels
        for i, v in enumerate(summary_df['Accuracy']):
            plt.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        # Save figure
        fig_path = os.path.join(self.output_dir, 'model_performance_comparison.png')
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')
        plt.close()

        # Save corresponding CSV data for the figure
        csv_path = os.path.join(self.output_dir, 'model_performance_comparison.csv')
        summary_df.to_csv(csv_path, index=False)

        print("Summary report generation completed")
        return summary_df

    def run_complete_pipeline(self):
        """Run complete machine learning pipeline"""
        print("=" * 60)
        print("Starting complete machine learning classification pipeline")
        print("=" * 60)

        # 1. Load data
        self.load_data()

        # 2. Feature engineering
        self.create_features()

        # 3. Prepare models
        self.prepare_models()

        # 4. Model evaluation
        self.evaluate_models()

        # 5. Generate confusion matrices
        self.generate_confusion_matrices()

        # 6. Generate classification reports
        self.generate_classification_reports()

        # 7. Generate ROC curves
        self.generate_roc_curves()

        # 8. Generate feature importance analysis
        self.generate_feature_importance()

        # 9. Generate summary report
        summary = self.generate_summary_report()

        print("=" * 60)
        print("Machine learning pipeline execution completed!")
        print("=" * 60)
        print("\nBest model performance:")
        print(summary.head())

        return summary


def main():
    """Main function"""
    # Create pipeline instance
    pipeline = EnzymeMLPipeline('sample0513.csv')

    # Run complete pipeline
    results = pipeline.run_complete_pipeline()

    print(f"\nAll result files have been generated in: {pipeline.output_dir}")
    print("Generated files:")
    print("- Confusion matrices: confusion_matrix_*.png with confusion_matrix_*.csv")
    print("- Classification reports: classification_report_*.csv")
    print("- ROC curves: roc_curves_*.png with roc_curves_*.csv and auc_summary_*.csv")
    print("- Feature importance: feature_importance_*.png with feature_importance_*.csv")
    print("- Model performance: model_performance_comparison.png with model_performance_comparison.csv")


if __name__ == "__main__":
    main()
