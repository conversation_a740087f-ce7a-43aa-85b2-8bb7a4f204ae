#!/usr/bin/env python3
"""
Clustering Analysis Module
=========================

This module handles hierarchical clustering analysis based on kinetic
parameters and key features.
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist

from .base import AnalysisModule


class ClusteringModule(AnalysisModule):
    """Module for hierarchical clustering analysis."""
    
    def get_module_name(self) -> str:
        return "Step 8: Hierarchical Clustering Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute clustering analysis."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Prepare clustering features
        cluster_features = self._prepare_clustering_features(df)
        
        # Perform hierarchical clustering
        clustering_results = self._perform_hierarchical_clustering(cluster_features)
        
        # Create visualizations
        self._create_clustering_visualizations(clustering_results, cluster_features)
        
        return clustering_results
    
    def _prepare_clustering_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for clustering analysis."""
        print("Preparing clustering features...")
        
        sample_features = []
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = df[df['sample'] == sample]
            
            # Calculate comprehensive features for clustering
            features = {
                'sample': sample,
                'TMB_mean': sample_data['TMB'].mean(),
                'TMB_std': sample_data['TMB'].std(),
                'TMB_max': sample_data['TMB'].max(),
                'TMB_min': sample_data['TMB'].min(),
                'TMB_range': sample_data['TMB'].max() - sample_data['TMB'].min(),
                'OPD_mean': sample_data['OPD'].mean(),
                'OPD_std': sample_data['OPD'].std(),
                'OPD_max': sample_data['OPD'].max(),
                'OPD_min': sample_data['OPD'].min(),
                'OPD_range': sample_data['OPD'].max() - sample_data['OPD'].min(),
                'TMB_OPD_ratio_mean': (sample_data['TMB'] / sample_data['OPD']).mean(),
                'TMB_OPD_ratio_std': (sample_data['TMB'] / sample_data['OPD']).std(),
                'concentration_sensitivity_TMB': 0,
                'concentration_sensitivity_OPD': 0
            }
            
            # Calculate concentration sensitivity (slope of response vs log concentration)
            if len(sample_data) > 2:
                log_conc = np.log10(sample_data['concentration_value'])
                try:
                    features['concentration_sensitivity_TMB'] = np.polyfit(log_conc, sample_data['TMB'], 1)[0]
                    features['concentration_sensitivity_OPD'] = np.polyfit(log_conc, sample_data['OPD'], 1)[0]
                except:
                    pass
            
            sample_features.append(features)
        
        feature_df = pd.DataFrame(sample_features)
        print(f"Clustering features shape: {feature_df.shape}")
        
        return feature_df
    
    def _perform_hierarchical_clustering(self, feature_df: pd.DataFrame) -> Dict[str, Any]:
        """Perform hierarchical clustering analysis."""
        print("Performing hierarchical clustering...")
        
        # Prepare feature matrix
        feature_columns = [col for col in feature_df.columns if col != 'sample']
        X = feature_df[feature_columns].values
        sample_names = feature_df['sample'].values
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Get clustering configuration
        config = self.config.get_module_config('clustering')
        n_clusters = config.get('n_clusters', 3)
        linkage_method = config.get('linkage_method', 'ward')
        distance_metric = config.get('distance_metric', 'euclidean')
        
        # Compute linkage matrix
        if linkage_method == 'ward':
            linkage_matrix = linkage(X_scaled, method='ward')
        else:
            distances = pdist(X_scaled, metric=distance_metric)
            linkage_matrix = linkage(distances, method=linkage_method)
        
        # Perform clustering
        clustering = AgglomerativeClustering(n_clusters=n_clusters, linkage=linkage_method)
        cluster_labels = clustering.fit_predict(X_scaled)
        
        # Store clustering results
        for i, sample in enumerate(sample_names):
            self.data_manager.add_result(
                analysis_step='clustering_results',
                sample=sample,
                cluster=cluster_labels[i]
            )
        
        results = {
            'feature_matrix': X_scaled,
            'feature_names': feature_columns,
            'sample_names': sample_names,
            'linkage_matrix': linkage_matrix,
            'cluster_labels': cluster_labels,
            'n_clusters': n_clusters,
            'scaler': scaler
        }
        
        print(f"Clustering completed. Samples assigned to {n_clusters} clusters.")
        for i, sample in enumerate(sample_names):
            print(f"  Sample {sample}: Cluster {cluster_labels[i]}")
        
        return results
    
    def _create_clustering_visualizations(self, clustering_results: Dict[str, Any], 
                                        feature_df: pd.DataFrame):
        """Create clustering visualization plots."""
        
        # Create dendrogram as required
        self._create_required_dendrogram(clustering_results)
        
        # Create clustermap as required
        self._create_required_clustermap(clustering_results, feature_df)
        
        # Create additional clustering plots
        self._create_additional_clustering_plots(clustering_results, feature_df)
    
    def _create_required_dendrogram(self, clustering_results: Dict[str, Any]):
        """Create hierarchical clustering dendrogram as specifically required."""
        def plot_dendrogram(ax):
            linkage_matrix = clustering_results['linkage_matrix']
            sample_names = clustering_results['sample_names']
            
            # Create dendrogram
            dendro = dendrogram(linkage_matrix, labels=sample_names, 
                              leaf_rotation=0, ax=ax)
            
            ax.set_title('Hierarchical Clustering Dendrogram', fontsize=16, fontweight='bold')
            ax.set_ylabel('Distance', fontsize=14)
            ax.set_xlabel('Sample', fontsize=14)
            ax.tick_params(labelsize=12)
            
            # Color the dendrogram branches by cluster
            cluster_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            n_clusters = clustering_results['n_clusters']
            
            return dendro
        
        # Prepare data for export
        dendro_data = pd.DataFrame({
            'sample': clustering_results['sample_names'],
            'cluster': clustering_results['cluster_labels']
        })
        
        self.plot_manager.save_single_plot('hierarchical_clustering_dendrogram.png', 
                                         plot_dendrogram, data=dendro_data)
    
    def _create_required_clustermap(self, clustering_results: Dict[str, Any], 
                                  feature_df: pd.DataFrame):
        """Create comprehensive clustermap as specifically required."""
        def plot_clustermap(ax):
            feature_matrix = clustering_results['feature_matrix']
            feature_names = clustering_results['feature_names']
            sample_names = clustering_results['sample_names']
            
            # Create heatmap
            im = ax.imshow(feature_matrix, cmap='RdBu_r', aspect='auto')
            
            # Set ticks and labels
            ax.set_xticks(range(len(feature_names)))
            ax.set_xticklabels(feature_names, rotation=45, ha='right')
            ax.set_yticks(range(len(sample_names)))
            ax.set_yticklabels(sample_names)
            
            # Add colorbar
            plt.colorbar(im, ax=ax, label='Standardized Value')
            
            ax.set_title('Feature Heatmap (Ordered by Clustering)', 
                        fontsize=16, fontweight='bold')
            ax.tick_params(labelsize=10)
        
        # Prepare data for export
        heatmap_data = pd.DataFrame(clustering_results['feature_matrix'],
                                   index=clustering_results['sample_names'],
                                   columns=clustering_results['feature_names'])
        heatmap_data['cluster'] = clustering_results['cluster_labels']
        
        self.plot_manager.save_single_plot('clustermap_summary.png', plot_clustermap, 
                                         data=heatmap_data)
    
    def _create_additional_clustering_plots(self, clustering_results: Dict[str, Any], 
                                          feature_df: pd.DataFrame):
        """Create additional clustering analysis plots."""
        
        # Cluster characteristics plot
        def plot_cluster_characteristics(ax):
            feature_matrix = clustering_results['feature_matrix']
            feature_names = clustering_results['feature_names']
            cluster_labels = clustering_results['cluster_labels']
            n_clusters = clustering_results['n_clusters']
            
            # Calculate cluster means
            cluster_means = []
            for cluster_id in range(n_clusters):
                mask = cluster_labels == cluster_id
                if np.any(mask):
                    cluster_mean = feature_matrix[mask].mean(axis=0)
                    cluster_means.append(cluster_mean)
            
            cluster_means = np.array(cluster_means)
            
            # Create heatmap of cluster characteristics
            im = ax.imshow(cluster_means, cmap='viridis', aspect='auto')
            
            ax.set_xticks(range(len(feature_names)))
            ax.set_xticklabels(feature_names, rotation=45, ha='right')
            ax.set_yticks(range(n_clusters))
            ax.set_yticklabels([f'Cluster {i}' for i in range(n_clusters)])
            
            plt.colorbar(im, ax=ax, label='Mean Standardized Value')
            ax.set_title('Cluster Characteristics', fontsize=16, fontweight='bold')
            ax.tick_params(labelsize=10)
        
        # Prepare cluster characteristics data
        cluster_chars = []
        for cluster_id in range(clustering_results['n_clusters']):
            mask = clustering_results['cluster_labels'] == cluster_id
            if np.any(mask):
                cluster_mean = clustering_results['feature_matrix'][mask].mean(axis=0)
                for i, feature in enumerate(clustering_results['feature_names']):
                    cluster_chars.append({
                        'cluster': cluster_id,
                        'feature': feature,
                        'mean_value': cluster_mean[i]
                    })
        
        cluster_chars_df = pd.DataFrame(cluster_chars)
        
        self.plot_manager.save_single_plot('cluster_characteristics.png', 
                                         plot_cluster_characteristics, data=cluster_chars_df)
