#!/usr/bin/env python3
"""
Quality Control Analysis Module
==============================

This module handles quality control analysis including CV calculation,
correlation analysis, and replicate consistency assessment.
"""

from typing import Dict, Any, List, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import pearsonr, spearmanr

from .base import AnalysisModule


class QualityControlModule(AnalysisModule):
    """Module for quality control and correlation analysis."""
    
    def get_module_name(self) -> str:
        return "Step 3: Quality Control and Correlation Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute quality control analysis."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Calculate CV% for replicates
        cv_results = self._calculate_cv_analysis(df)
        
        # Correlation analysis
        correlation_results = self._perform_correlation_analysis(df)
        
        # Generate visualizations
        self._create_quality_control_plots(df, cv_results)
        
        return {
            'cv_analysis': cv_results,
            'correlation_analysis': correlation_results
        }
    
    def _calculate_cv_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate coefficient of variation for parallel replicates."""
        cv_results = []
        cv_threshold = self.config.get_module_config('quality_control', 'cv_threshold', 15.0)
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            for conc in df['concentration_value'].unique():
                subset = df[(df['sample'] == sample) & 
                           (df['concentration_value'] == conc)]
                
                for substrate in ['TMB', 'OPD']:
                    values = subset[substrate]
                    if len(values) > 1:
                        mean_val = values.mean()
                        std_val = values.std()
                        cv = (std_val / mean_val) * 100
                        
                        # Calculate ICC (Intraclass Correlation Coefficient)
                        # Using one-way random effects model approximation
                        n = len(values)
                        if n > 2:
                            between_var = 0  # Since we only have one group per sample-conc
                            within_var = std_val ** 2
                            icc = max(0, between_var / (between_var + within_var)) if between_var + within_var > 0 else 0
                        else:
                            icc = np.nan
                        
                        cv_data = {
                            'sample': sample,
                            'concentration': conc,
                            'substrate': substrate,
                            'cv_percent': cv,
                            'icc': icc,
                            'mean': mean_val,
                            'std': std_val,
                            'n_replicates': len(values),
                            'exceeds_threshold': cv > cv_threshold
                        }
                        
                        cv_results.append(cv_data)
                        
                        # Store in results
                        self.data_manager.add_result(
                            analysis_step='quality_control',
                            **cv_data
                        )
        
        cv_df = pd.DataFrame(cv_results)
        
        # Summary statistics
        summary = {
            'overall_stats': {
                'mean_cv_tmb': cv_df[cv_df['substrate'] == 'TMB']['cv_percent'].mean(),
                'mean_cv_opd': cv_df[cv_df['substrate'] == 'OPD']['cv_percent'].mean(),
                'max_cv_tmb': cv_df[cv_df['substrate'] == 'TMB']['cv_percent'].max(),
                'max_cv_opd': cv_df[cv_df['substrate'] == 'OPD']['cv_percent'].max(),
                'threshold_exceeded_count': cv_df['exceeds_threshold'].sum(),
                'total_measurements': len(cv_df)
            },
            'detailed_results': cv_results
        }
        
        print(f"CV% Analysis Summary:")
        print(f"Mean CV% (TMB): {summary['overall_stats']['mean_cv_tmb']:.2f}%")
        print(f"Mean CV% (OPD): {summary['overall_stats']['mean_cv_opd']:.2f}%")
        print(f"Measurements exceeding {cv_threshold}% threshold: {summary['overall_stats']['threshold_exceeded_count']}")
        
        return summary
    
    def _perform_correlation_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform comprehensive correlation analysis."""
        results = {}
        
        # Overall TMB-OPD correlation
        tmb_opd_pearson = pearsonr(df['TMB'], df['OPD'])
        tmb_opd_spearman = spearmanr(df['TMB'], df['OPD'])
        
        results['overall_correlation'] = {
            'pearson_r': tmb_opd_pearson[0],
            'pearson_p': tmb_opd_pearson[1],
            'spearman_r': tmb_opd_spearman[0],
            'spearman_p': tmb_opd_spearman[1]
        }
        
        # Store overall correlation
        self.data_manager.add_result(
            analysis_step='correlation_analysis',
            correlation_type='overall_tmb_opd',
            pearson_r=tmb_opd_pearson[0],
            pearson_p=tmb_opd_pearson[1],
            spearman_r=tmb_opd_spearman[0],
            spearman_p=tmb_opd_spearman[1]
        )
        
        # Correlation by sample
        results['by_sample'] = {}
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = df[df['sample'] == sample]
            if len(sample_data) > 2:  # Need at least 3 points for meaningful correlation
                pearson = pearsonr(sample_data['TMB'], sample_data['OPD'])
                spearman = spearmanr(sample_data['TMB'], sample_data['OPD'])
                
                results['by_sample'][sample] = {
                    'pearson_r': pearson[0],
                    'pearson_p': pearson[1],
                    'spearman_r': spearman[0],
                    'spearman_p': spearman[1],
                    'n_points': len(sample_data)
                }
                
                # Store sample-specific correlation
                self.data_manager.add_result(
                    analysis_step='correlation_analysis',
                    correlation_type='by_sample',
                    sample=sample,
                    pearson_r=pearson[0],
                    pearson_p=pearson[1],
                    spearman_r=spearman[0],
                    spearman_p=spearman[1],
                    n_points=len(sample_data)
                )
        
        # Correlation with concentration
        results['with_concentration'] = {}
        for substrate in ['TMB', 'OPD']:
            conc_corr = pearsonr(df['concentration_value'], df[substrate])
            log_conc_corr = pearsonr(np.log10(df['concentration_value']), df[substrate])
            
            results['with_concentration'][substrate] = {
                'linear_r': conc_corr[0],
                'linear_p': conc_corr[1],
                'log_r': log_conc_corr[0],
                'log_p': log_conc_corr[1]
            }
            
            # Store concentration correlation
            self.data_manager.add_result(
                analysis_step='correlation_analysis',
                correlation_type='with_concentration',
                substrate=substrate,
                linear_r=conc_corr[0],
                linear_p=conc_corr[1],
                log_r=log_conc_corr[0],
                log_p=log_conc_corr[1]
            )
        
        print(f"TMB-OPD Correlation: r = {tmb_opd_pearson[0]:.4f}, p = {tmb_opd_pearson[1]:.4f}")
        
        return results
    
    def _create_quality_control_plots(self, df: pd.DataFrame, cv_results: Dict):
        """Create quality control visualization plots."""
        print("Creating quality control plots...")
        
        # 1. Correlation plot
        plt.figure(figsize=(10, 8))
        sns.scatterplot(data=df, x='TMB', y='OPD', hue='sample', s=100, alpha=0.7)
        
        # Add regression line
        from scipy import stats
        slope, intercept, r_value, p_value, std_err = stats.linregress(df['TMB'], df['OPD'])
        line = slope * df['TMB'] + intercept
        plt.plot(df['TMB'], line, 'r--', alpha=0.8, linewidth=2)
        
        plt.title(f'TMB vs OPD Correlation (R² = {r_value**2:.3f})', fontsize=16, fontweight='bold')
        plt.xlabel('TMB Activity', fontsize=14)
        plt.ylabel('OPD Activity', fontsize=14)
        plt.legend(title='Sample', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filepath = f"{self.data_manager.output_dir}/visualizations/correlation_TMB_vs_OPD.png"
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"✅ Correlation plot saved: {filepath}")
        
        # 2. CV Heatmap
        cv_data = cv_results.get('cv_summary', {})
        if cv_data:
            plt.figure(figsize=(12, 8))
            
            # Prepare CV data for heatmap
            cv_matrix_data = []
            for key, cv_val in cv_data.items():
                if 'TMB_cv' in key:
                    sample, conc = key.replace('_TMB_cv', '').split('_')
                    cv_matrix_data.append({'Sample': sample, 'Concentration': conc, 'TMB_CV': cv_val, 'Substrate': 'TMB'})
                elif 'OPD_cv' in key:
                    sample, conc = key.replace('_OPD_cv', '').split('_')
                    cv_matrix_data.append({'Sample': sample, 'Concentration': conc, 'OPD_CV': cv_val, 'Substrate': 'OPD'})
            
            if cv_matrix_data:
                cv_df = pd.DataFrame(cv_matrix_data)
                
                # Create pivot table for heatmap
                for substrate in ['TMB', 'OPD']:
                    substrate_data = cv_df[cv_df['Substrate'] == substrate]
                    if not substrate_data.empty:
                        pivot_data = substrate_data.pivot(index='Sample', columns='Concentration', values=f'{substrate}_CV')
                        
                        plt.figure(figsize=(10, 6))
                        sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                                   center=15, vmin=0, vmax=30, cbar_kws={'label': 'CV%'})
                        plt.title(f'{substrate} Coefficient of Variation (CV%) Heatmap', fontsize=16, fontweight='bold')
                        plt.xlabel('Concentration', fontsize=14)
                        plt.ylabel('Sample', fontsize=14)
                        plt.tight_layout()
                        
                        filepath = f"{self.data_manager.output_dir}/visualizations/cv_heatmap_{substrate}.png"
                        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
                        plt.close()
                        print(f"✅ {substrate} CV heatmap saved: {filepath}")
    
    def _create_required_correlation_plot(self, df: pd.DataFrame):
        """Create TMB vs OPD correlation plot as specifically required."""
        
        # 1. 基础相关性图 (与原代码保持一致)
        def plot_correlation(ax):
            correlation = pearsonr(df['TMB'], df['OPD'])
            
            # Scatter plot
            ax.scatter(df['TMB'], df['OPD'], alpha=0.7, s=60, c='steelblue', edgecolors='black', linewidth=0.5)
            
            # Add regression line
            z = np.polyfit(df['TMB'], df['OPD'], 1)
            p = np.poly1d(z)
            ax.plot(df['TMB'], p(df['TMB']), "r--", linewidth=3, alpha=0.8)
            
            # Calculate R-squared
            r_squared = correlation[0] ** 2
            
            ax.set_xlabel('TMB Activity', fontsize=14)
            ax.set_ylabel('OPD Activity', fontsize=14)
            ax.set_title(f'TMB vs OPD Correlation\nR = {correlation[0]:.4f}, R² = {r_squared:.4f}, p = {correlation[1]:.4f}', 
                        fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
            
            # Add statistics text box
            stats_text = f'n = {len(df)}\nR = {correlation[0]:.4f}\nR² = {r_squared:.4f}\np = {correlation[1]:.4f}'
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=12,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # Prepare data for export
        corr_data = df[['TMB', 'OPD']].copy()
        corr_data['regression_line'] = np.poly1d(np.polyfit(df['TMB'], df['OPD'], 1))(df['TMB'])
        
        self.plot_manager.save_single_plot('correlation_TMB_vs_OPD.png', plot_correlation, data=corr_data)
        
        # 2. 浓度-活性回归分析 (正常X轴)
        self._create_concentration_regression_plots(df)
    
    def _create_concentration_regression_plots(self, df: pd.DataFrame):
        """Create concentration-activity regression plots with normal and log X-axis."""
        
        # 为每个样本创建回归分析图
        def plot_concentration_regression_normal(ax):
            """正常X轴的浓度-活性回归"""
            palette = sns.color_palette("viridis", len(df['sample'].unique()))
            
            for i, sample in enumerate(['A', 'B', 'C', 'D', 'E', 'F']):
                sample_data = df[df['sample'] == sample]
                if len(sample_data) > 1:
                    # TMB回归
                    ax.scatter(sample_data['concentration_value'], sample_data['TMB'], 
                             color=palette[i], alpha=0.7, s=60, label=f'Sample {sample}')
                    
                    # 添加回归线
                    z = np.polyfit(sample_data['concentration_value'], sample_data['TMB'], 1)
                    p = np.poly1d(z)
                    x_range = np.linspace(sample_data['concentration_value'].min(), 
                                        sample_data['concentration_value'].max(), 100)
                    ax.plot(x_range, p(x_range), color=palette[i], linestyle='--', alpha=0.8)
            
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('TMB Activity', fontsize=14)
            ax.set_title('TMB Activity vs Concentration (Linear Scale)', fontsize=16, fontweight='bold')
            ax.legend(fontsize=10, ncol=2)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        def plot_concentration_regression_log(ax):
            """对数X轴的浓度-活性回归"""
            palette = sns.color_palette("viridis", len(df['sample'].unique()))
            
            for i, sample in enumerate(['A', 'B', 'C', 'D', 'E', 'F']):
                sample_data = df[df['sample'] == sample]
                if len(sample_data) > 1:
                    # TMB回归
                    ax.scatter(sample_data['concentration_value'], sample_data['TMB'], 
                             color=palette[i], alpha=0.7, s=60, label=f'Sample {sample}')
                    
                    # 添加回归线 (对数空间)
                    log_conc = np.log10(sample_data['concentration_value'])
                    z = np.polyfit(log_conc, sample_data['TMB'], 1)
                    p = np.poly1d(z)
                    x_range = np.logspace(np.log10(sample_data['concentration_value'].min()), 
                                        np.log10(sample_data['concentration_value'].max()), 100)
                    y_fit = p(np.log10(x_range))
                    ax.plot(x_range, y_fit, color=palette[i], linestyle='--', alpha=0.8)
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM, log scale)', fontsize=14)
            ax.set_ylabel('TMB Activity', fontsize=14)
            ax.set_title('TMB Activity vs Concentration (Log Scale)', fontsize=16, fontweight='bold')
            ax.legend(fontsize=10, ncol=2)
            ax.grid(True, alpha=0.3, which="both", ls="--")
            ax.tick_params(labelsize=12)
        
        def plot_opd_concentration_regression_normal(ax):
            """OPD正常X轴的浓度-活性回归"""
            palette = sns.color_palette("plasma", len(df['sample'].unique()))
            
            for i, sample in enumerate(['A', 'B', 'C', 'D', 'E', 'F']):
                sample_data = df[df['sample'] == sample]
                if len(sample_data) > 1:
                    # OPD回归
                    ax.scatter(sample_data['concentration_value'], sample_data['OPD'], 
                             color=palette[i], alpha=0.7, s=60, label=f'Sample {sample}')
                    
                    # 添加回归线
                    z = np.polyfit(sample_data['concentration_value'], sample_data['OPD'], 1)
                    p = np.poly1d(z)
                    x_range = np.linspace(sample_data['concentration_value'].min(), 
                                        sample_data['concentration_value'].max(), 100)
                    ax.plot(x_range, p(x_range), color=palette[i], linestyle='--', alpha=0.8)
            
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('OPD Activity', fontsize=14)
            ax.set_title('OPD Activity vs Concentration (Linear Scale)', fontsize=16, fontweight='bold')
            ax.legend(fontsize=10, ncol=2)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        def plot_opd_concentration_regression_log(ax):
            """OPD对数X轴的浓度-活性回归"""
            palette = sns.color_palette("plasma", len(df['sample'].unique()))
            
            for i, sample in enumerate(['A', 'B', 'C', 'D', 'E', 'F']):
                sample_data = df[df['sample'] == sample]
                if len(sample_data) > 1:
                    # OPD回归
                    ax.scatter(sample_data['concentration_value'], sample_data['OPD'], 
                             color=palette[i], alpha=0.7, s=60, label=f'Sample {sample}')
                    
                    # 添加回归线 (对数空间)
                    log_conc = np.log10(sample_data['concentration_value'])
                    z = np.polyfit(log_conc, sample_data['OPD'], 1)
                    p = np.poly1d(z)
                    x_range = np.logspace(np.log10(sample_data['concentration_value'].min()), 
                                        np.log10(sample_data['concentration_value'].max()), 100)
                    y_fit = p(np.log10(x_range))
                    ax.plot(x_range, y_fit, color=palette[i], linestyle='--', alpha=0.8)
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM, log scale)', fontsize=14)
            ax.set_ylabel('OPD Activity', fontsize=14)
            ax.set_title('OPD Activity vs Concentration (Log Scale)', fontsize=16, fontweight='bold')
            ax.legend(fontsize=10, ncol=2)
            ax.grid(True, alpha=0.3, which="both", ls="--")
            ax.tick_params(labelsize=12)
        
        # 准备回归数据
        regression_data = df[['sample', 'concentration_value', 'TMB', 'OPD']].copy()
        
        # 保存各种回归图
        self.plot_manager.save_single_plot('concentration_regression_TMB_linear.png', 
                                         plot_concentration_regression_normal, data=regression_data)
        self.plot_manager.save_single_plot('concentration_regression_TMB_log.png', 
                                         plot_concentration_regression_log, data=regression_data)
        self.plot_manager.save_single_plot('concentration_regression_OPD_linear.png', 
                                         plot_opd_concentration_regression_normal, data=regression_data)
        self.plot_manager.save_single_plot('concentration_regression_OPD_log.png', 
                                         plot_opd_concentration_regression_log, data=regression_data)
    
    def _create_required_cv_heatmap(self, cv_results: Dict[str, Any]):
        """Create CV% heatmap as specifically required."""
        def plot_cv_heatmap(ax):
            cv_df = pd.DataFrame(cv_results['detailed_results'])
            cv_threshold = self.config.get_module_config('quality_control', 'cv_threshold', 15.0)
            
            # Create combined heatmap for both substrates
            tmb_data = cv_df[cv_df['substrate'] == 'TMB'].pivot(
                index='sample', columns='concentration', values='cv_percent')
            opd_data = cv_df[cv_df['substrate'] == 'OPD'].pivot(
                index='sample', columns='concentration', values='cv_percent')
            
            # Combine data for visualization
            combined_data = pd.concat([tmb_data, opd_data], axis=1, keys=['TMB', 'OPD'])
            
            # Create heatmap
            sns.heatmap(combined_data, annot=True, fmt='.1f', cmap='RdYlBu_r',
                       ax=ax, cbar_kws={'label': 'CV%'})
            ax.set_title('CV% Heatmap for TMB and OPD', fontsize=16, fontweight='bold')
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('Sample', fontsize=14)
            
            # Highlight CV > threshold
            for i, row in enumerate(combined_data.index):
                for j, col in enumerate(combined_data.columns):
                    value = combined_data.iloc[i, j]
                    if not np.isnan(value) and value > cv_threshold:
                        ax.add_patch(plt.Rectangle((j, i), 1, 1, fill=False, 
                                                 edgecolor='red', lw=4))
        
        # Prepare data for export
        cv_export_data = pd.DataFrame(cv_results['detailed_results'])
        self.plot_manager.save_single_plot('cv_heatmap.png', plot_cv_heatmap, data=cv_export_data)

    def _create_comprehensive_qc_dashboard(self, df: pd.DataFrame, cv_results: Dict[str, Any]):
        """Create comprehensive quality control dashboard for overview."""
        fig = plt.figure(figsize=(20, 15))
        
        # Layout: 3 rows, 3 columns
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. Overall TMB vs OPD correlation
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_correlation_scatter(df, ax1)
        
        # 2. CV% heatmaps
        ax2 = fig.add_subplot(gs[0, 1])
        ax3 = fig.add_subplot(gs[0, 2])
        self._plot_cv_heatmaps(cv_results, ax2, ax3)
        
        # 3. Correlation by sample
        ax4 = fig.add_subplot(gs[1, :])
        self._plot_correlation_by_sample(df, ax4)
        
        # 4. Concentration vs activity correlations
        ax5 = fig.add_subplot(gs[2, 0])
        ax6 = fig.add_subplot(gs[2, 1])
        self._plot_concentration_correlations(df, ax5, ax6)
        
        # 5. CV% distribution
        ax7 = fig.add_subplot(gs[2, 2])
        self._plot_cv_distribution(cv_results, ax7)
        
        plt.suptitle('Quality Control Analysis Dashboard', fontsize=16, fontweight='bold')
        self.plot_manager.save_plot('quality_control_comprehensive.png')
    
    def _plot_correlation_scatter(self, df: pd.DataFrame, ax):
        """Plot TMB vs OPD correlation scatter."""
        correlation = pearsonr(df['TMB'], df['OPD'])
        
        ax.scatter(df['TMB'], df['OPD'], alpha=0.7, s=50)
        
        # Add regression line
        z = np.polyfit(df['TMB'], df['OPD'], 1)
        p = np.poly1d(z)
        ax.plot(df['TMB'], p(df['TMB']), "r--", alpha=0.8, linewidth=2)
        
        ax.set_xlabel('TMB Activity')
        ax.set_ylabel('OPD Activity')
        ax.set_title(f'TMB vs OPD Correlation\nR = {correlation[0]:.4f}, p = {correlation[1]:.4f}')
        ax.grid(True, alpha=0.3)
    
    def _plot_cv_heatmaps(self, cv_results: Dict[str, Any], ax1, ax2):
        """Plot CV% heatmaps for TMB and OPD."""
        cv_df = pd.DataFrame(cv_results['detailed_results'])
        cv_threshold = self.config.get_module_config('quality_control', 'cv_threshold', 15.0)
        
        for i, substrate in enumerate(['TMB', 'OPD']):
            ax = ax1 if i == 0 else ax2
            
            # Create pivot table
            cv_pivot = cv_df[cv_df['substrate'] == substrate].pivot(
                index='sample', columns='concentration', values='cv_percent')
            
            # Create heatmap
            sns.heatmap(cv_pivot, annot=True, fmt='.1f', cmap='RdYlBu_r',
                       ax=ax, cbar_kws={'label': 'CV%'})
            ax.set_title(f'{substrate} CV% Heatmap')
            
            # Highlight CV > threshold
            for (i_idx, j_idx), value in np.ndenumerate(cv_pivot.values):
                if not np.isnan(value) and value > cv_threshold:
                    ax.add_patch(plt.Rectangle((j_idx, i_idx), 1, 1,
                                             fill=False, edgecolor='red',
                                             lw=3))
    
    def _plot_correlation_by_sample(self, df: pd.DataFrame, ax):
        """Plot TMB vs OPD correlation for each sample."""
        samples = ['A', 'B', 'C', 'D', 'E', 'F']
        colors = plt.cm.Set1(np.linspace(0, 1, len(samples)))
        
        for i, sample in enumerate(samples):
            sample_data = df[df['sample'] == sample]
            ax.scatter(sample_data['TMB'], sample_data['OPD'], 
                      label=f'Sample {sample}', alpha=0.7, 
                      color=colors[i], s=50)
            
            # Add individual regression lines
            if len(sample_data) > 2:
                z = np.polyfit(sample_data['TMB'], sample_data['OPD'], 1)
                p = np.poly1d(z)
                x_range = np.linspace(sample_data['TMB'].min(), sample_data['TMB'].max(), 50)
                ax.plot(x_range, p(x_range), color=colors[i], linestyle='--', alpha=0.6)
        
        ax.set_xlabel('TMB Activity')
        ax.set_ylabel('OPD Activity')
        ax.set_title('TMB vs OPD Correlation by Sample')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_concentration_correlations(self, df: pd.DataFrame, ax1, ax2):
        """Plot concentration vs activity correlations."""
        substrates = ['TMB', 'OPD']
        axes = [ax1, ax2]
        
        for i, substrate in enumerate(substrates):
            ax = axes[i]
            
            # Linear scale
            corr_linear = pearsonr(df['concentration_value'], df[substrate])
            ax.scatter(df['concentration_value'], df[substrate], alpha=0.7, s=50)
            
            z = np.polyfit(df['concentration_value'], df[substrate], 1)
            p = np.poly1d(z)
            ax.plot(df['concentration_value'], p(df['concentration_value']), 
                   "r--", alpha=0.8, linewidth=2)
            
            ax.set_xlabel('Concentration (μM)')
            ax.set_ylabel(f'{substrate} Activity')
            ax.set_title(f'Concentration vs {substrate}\nR = {corr_linear[0]:.4f}')
            ax.grid(True, alpha=0.3)
    
    def _plot_cv_distribution(self, cv_results: Dict[str, Any], ax):
        """Plot CV% distribution."""
        cv_df = pd.DataFrame(cv_results['detailed_results'])
        cv_threshold = self.config.get_module_config('quality_control', 'cv_threshold', 15.0)
        
        # Create histogram
        for substrate in ['TMB', 'OPD']:
            substrate_cvs = cv_df[cv_df['substrate'] == substrate]['cv_percent']
            ax.hist(substrate_cvs, bins=15, alpha=0.7, label=f'{substrate} CV%', density=True)
        
        # Add threshold line
        ax.axvline(cv_threshold, color='red', linestyle='--', linewidth=2, 
                  label=f'Threshold ({cv_threshold}%)')
        
        ax.set_xlabel('CV%')
        ax.set_ylabel('Density')
        ax.set_title('CV% Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)
