#!/usr/bin/env python3
"""
Model Explainability Analysis Module
===================================

This module handles SHAP analysis and t-SNE visualization for model
explainability and feature interpretation.
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE

# SHAP import with error handling
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("SHAP not available. SHAP analysis will be skipped.")

from .base import AnalysisModule


class ExplainabilityModule(AnalysisModule):
    """Module for model explainability analysis using SHAP and t-SNE."""
    
    def get_module_name(self) -> str:
        return "Step 7: Model Explainability Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute explainability analysis."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Get the trained model (assuming it's stored somewhere)
        # For this implementation, we'll recreate the features and model
        features, target, model = self._prepare_model_and_features(df)
        
        # SHAP analysis
        shap_results = self._perform_shap_analysis(model, features, target)
        
        # t-SNE analysis  
        tsne_results = self._perform_tsne_analysis(features, target)
        
        # Create visualizations
        self._create_explainability_visualizations(shap_results, tsne_results, features, target)
        
        return {
            'shap_results': shap_results,
            'tsne_results': tsne_results
        }
    
    def _prepare_model_and_features(self, df):
        """Prepare model and features for explainability analysis."""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.preprocessing import StandardScaler, LabelEncoder
        
        # Feature engineering (same as ML module)
        ml_data = df.copy()
        ml_data['TMB_OPD_ratio'] = ml_data['TMB'] / ml_data['OPD']
        ml_data['log_concentration'] = np.log10(ml_data['concentration_value'])
        ml_data['TMB_squared'] = ml_data['TMB'] ** 2
        ml_data['OPD_squared'] = ml_data['OPD'] ** 2
        ml_data['TMB_OPD_product'] = ml_data['TMB'] * ml_data['OPD']
        
        # Create sample-level features
        sample_features = []
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = ml_data[ml_data['sample'] == sample]
            
            features = {
                'sample': sample,
                'TMB_mean': sample_data['TMB'].mean(),
                'TMB_std': sample_data['TMB'].std(),
                'TMB_max': sample_data['TMB'].max(),
                'TMB_min': sample_data['TMB'].min(),
                'OPD_mean': sample_data['OPD'].mean(),
                'OPD_std': sample_data['OPD'].std(),
                'OPD_max': sample_data['OPD'].max(),
                'OPD_min': sample_data['OPD'].min(),
                'ratio_mean': sample_data['TMB_OPD_ratio'].mean(),
                'ratio_std': sample_data['TMB_OPD_ratio'].std(),
            }
            
            sample_features.append(features)
        
        feature_df = pd.DataFrame(sample_features)
        feature_columns = [col for col in feature_df.columns if col != 'sample']
        X = feature_df[feature_columns]
        y = feature_df['sample'].values
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train a Random Forest model (SHAP-compatible)
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_scaled, y_encoded)
        
        return X_scaled, y, model
    
    def _perform_shap_analysis(self, model, features, target) -> Dict[str, Any]:
        """Perform SHAP analysis for model explainability."""
        if not SHAP_AVAILABLE:
            return {'error': 'SHAP not available'}
        
        try:
            print("Performing SHAP analysis...")
            
            # Create SHAP explainer
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(features)
            
            # Calculate feature importance
            if len(shap_values.shape) == 3:  # Multi-class
                mean_shap = np.abs(shap_values).mean(axis=(0, 2))
            else:
                mean_shap = np.abs(shap_values).mean(axis=0)
            
            # Store feature importance
            feature_names = ['TMB_mean', 'TMB_std', 'TMB_max', 'TMB_min', 
                           'OPD_mean', 'OPD_std', 'OPD_max', 'OPD_min', 
                           'ratio_mean', 'ratio_std']
            
            for i, feature in enumerate(feature_names):
                self.data_manager.add_result(
                    analysis_step='shap_feature_importance',
                    feature=feature,
                    mean_absolute_shap=mean_shap[i]
                )
            
            return {
                'explainer': explainer,
                'shap_values': shap_values,
                'feature_importance': mean_shap,
                'feature_names': feature_names
            }
            
        except Exception as e:
            print(f"SHAP analysis failed: {e}")
            return {'error': str(e)}
    
    def _perform_tsne_analysis(self, features, target) -> Dict[str, Any]:
        """Perform t-SNE dimensionality reduction with configurable parameters."""
        try:
            print("Performing t-SNE analysis...")
            
            # 获取聚类配置中的t-SNE参数
            config = self.config.get_module_config('clustering')
            
            # 配置t-SNE参数 (为小数据集优化)
            n_components = config.get('tsne_n_components', 2)           # 输出维度
            perplexity = min(config.get('tsne_perplexity', 3), len(features) // 3)  # 困惑度，小数据集用小值
            learning_rate = config.get('tsne_learning_rate', 200)      # 学习率
            n_iter = config.get('tsne_n_iter', 1000)                   # 迭代次数
            init = config.get('tsne_init', 'random')                   # 初始化方法
            metric = config.get('tsne_metric', 'euclidean')            # 距离度量
            early_exaggeration = config.get('tsne_early_exaggeration', 12.0)  # 早期夸大
            min_grad_norm = config.get('tsne_min_grad_norm', 1e-7)     # 最小梯度范数
            
            print(f"  t-SNE参数: perplexity={perplexity}, learning_rate={learning_rate}, "
                  f"n_iter={n_iter}, init={init}")
            
            # PCA预处理 (可选)
            use_pca = config.get('use_pca_preprocessing', True)
            if use_pca and len(features[0]) > 10:  # 只有当特征维度较高时才使用PCA
                from sklearn.decomposition import PCA
                pca_components = config.get('pca_n_components', 0.95)
                pca = PCA(n_components=pca_components, whiten=config.get('pca_whiten', False))
                features_pca = pca.fit_transform(features)
                print(f"  PCA预处理: {len(features[0])} -> {len(features_pca[0])} 维")
                features_for_tsne = features_pca
            else:
                features_for_tsne = features
            
            # 创建t-SNE对象 (兼容不同sklearn版本)
            tsne_params = {
                'n_components': n_components,
                'perplexity': perplexity,
                'learning_rate': learning_rate,
                'init': init,
                'metric': metric,
                'early_exaggeration': early_exaggeration,
                'min_grad_norm': min_grad_norm,
                'random_state': 42,
                'verbose': 1
            }
            
            # 检查sklearn版本，某些版本使用max_iter而不是n_iter
            try:
                # 尝试使用max_iter (新版本)
                tsne_params['max_iter'] = n_iter
                tsne = TSNE(**tsne_params)
            except TypeError:
                # 如果失败，移除不支持的参数
                tsne_params.pop('max_iter', None)
                # 尝试不同的参数组合
                try:
                    tsne_params['n_iter'] = n_iter  # 某些版本使用n_iter
                    tsne = TSNE(**tsne_params)
                except TypeError:
                    # 如果还是失败，使用最基本的参数
                    basic_params = {
                        'n_components': n_components,
                        'perplexity': perplexity,
                        'learning_rate': learning_rate,
                        'random_state': 42
                    }
                    tsne = TSNE(**basic_params)
            
            # 执行t-SNE降维
            X_tsne = tsne.fit_transform(features_for_tsne)
            
            print(f"  t-SNE完成: KL散度 = {tsne.kl_divergence_:.4f}")
            
            return {
                'embedding': X_tsne,
                'target': target,
                'kl_divergence': tsne.kl_divergence_,
                'n_iter_final': tsne.n_iter_,
                'perplexity_used': perplexity,
                'pca_used': use_pca and len(features[0]) > 10
            }
            
        except Exception as e:
            print(f"t-SNE analysis failed: {e}")
            return {'error': str(e)}
    
    def _create_explainability_visualizations(self, shap_results, tsne_results, features, target):
        """Create explainability visualization plots."""
        
        # Create SHAP summary plot as required
        if 'error' not in shap_results:
            self._create_required_shap_plot(shap_results, features)
        
        # Create t-SNE plot as required
        if 'error' not in tsne_results:
            self._create_required_tsne_plot(tsne_results)
        
        # Create additional explainability plots
        self._create_additional_explainability_plots(shap_results, tsne_results)
    
    def _create_required_shap_plot(self, shap_results, features):
        """Create SHAP summary plot as specifically required."""
        if not SHAP_AVAILABLE:
            return
        
        def plot_shap_summary(ax):
            # Use matplotlib to create SHAP-style plot
            feature_importance = shap_results['feature_importance']
            feature_names = shap_results['feature_names']
            
            # Sort features by importance
            sorted_idx = np.argsort(feature_importance)
            
            # Create horizontal bar plot
            ax.barh(range(len(feature_importance)), feature_importance[sorted_idx], 
                   color='steelblue', alpha=0.7)
            ax.set_yticks(range(len(feature_importance)))
            ax.set_yticklabels([feature_names[i] for i in sorted_idx])
            ax.set_xlabel('Mean |SHAP Value|', fontsize=14)
            ax.set_title('SHAP Feature Importance Summary', fontsize=16, fontweight='bold')
            ax.tick_params(labelsize=12)
            ax.grid(True, alpha=0.3, axis='x')
        
        # Prepare data for export
        shap_data = pd.DataFrame({
            'feature': shap_results['feature_names'],
            'mean_absolute_shap': shap_results['feature_importance']
        }).sort_values('mean_absolute_shap', ascending=False)
        
        self.plot_manager.save_single_plot('shap_summary_plot.png', plot_shap_summary, 
                                         data=shap_data)
    
    def _create_required_tsne_plot(self, tsne_results):
        """Create t-SNE plot as specifically required."""
        def plot_tsne(ax):
            X_tsne = tsne_results['embedding']
            target = tsne_results['target']
            
            # Create scatter plot with different colors for each sample
            samples = ['A', 'B', 'C', 'D', 'E', 'F']
            colors = plt.cm.Set1(np.linspace(0, 1, len(samples)))
            
            for i, sample in enumerate(samples):
                mask = target == sample
                if np.any(mask):
                    ax.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                             c=[colors[i]], label=f'Sample {sample}', 
                             s=150, alpha=0.8, edgecolors='black', linewidth=1)
            
            ax.set_xlabel('t-SNE Component 1', fontsize=14)
            ax.set_ylabel('t-SNE Component 2', fontsize=14)
            ax.set_title('t-SNE Visualization of Sample Clustering', fontsize=16, fontweight='bold')
            ax.legend(fontsize=12, frameon=True, shadow=True)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        # Prepare data for export
        tsne_data = pd.DataFrame({
            'tsne_1': tsne_results['embedding'][:, 0],
            'tsne_2': tsne_results['embedding'][:, 1],
            'sample': tsne_results['target']
        })
        
        self.plot_manager.save_single_plot('tsne_visualization.png', plot_tsne, 
                                         data=tsne_data)
    
    def _create_additional_explainability_plots(self, shap_results, tsne_results):
        """Create additional explainability plots."""
        
        # Feature importance comparison
        if 'error' not in shap_results:
            def plot_feature_ranking(ax):
                feature_importance = shap_results['feature_importance']
                feature_names = shap_results['feature_names']
                
                # Create ranking plot
                sorted_idx = np.argsort(feature_importance)[::-1]
                
                ax.plot(range(len(feature_importance)), feature_importance[sorted_idx], 
                       'o-', linewidth=2, markersize=8, color='darkred')
                ax.set_xticks(range(len(feature_importance)))
                ax.set_xticklabels([feature_names[i] for i in sorted_idx], rotation=45, ha='right')
                ax.set_ylabel('SHAP Importance', fontsize=14)
                ax.set_title('Feature Importance Ranking', fontsize=16, fontweight='bold')
                ax.tick_params(labelsize=10)
                ax.grid(True, alpha=0.3)
            
            ranking_data = pd.DataFrame({
                'feature': [shap_results['feature_names'][i] for i in np.argsort(shap_results['feature_importance'])[::-1]],
                'importance': sorted(shap_results['feature_importance'], reverse=True),
                'rank': range(1, len(shap_results['feature_importance']) + 1)
            })
            
            self.plot_manager.save_single_plot('feature_importance_ranking.png', 
                                             plot_feature_ranking, data=ranking_data)
