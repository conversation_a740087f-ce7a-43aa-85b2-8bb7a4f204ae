#!/usr/bin/env python3
"""
Descriptive Statistics and Visualization Module
==============================================

This module handles basic data exploration, descriptive statistics,
and initial visualizations.
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

from .base import AnalysisModule


class DescriptiveStatsModule(AnalysisModule):
    """Module for descriptive statistics and basic visualizations."""
    
    def get_module_name(self) -> str:
        return "Step 1: Data Loading and Descriptive Statistics"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute descriptive statistics analysis."""
        self.print_header()
        
        # Load data
        df = self.data_manager.load_data()
        self.data_manager.create_output_directories()
        
        # Calculate descriptive statistics
        desc_stats = self._calculate_descriptive_stats(df)
        
        # Generate visualizations
        self._create_distribution_plots(df)
        self._create_concentration_response_plots(df)
        
        return {
            'descriptive_stats': desc_stats,
            'data_shape': df.shape,
            'concentration_levels': sorted(df['concentration_value'].unique()),
            'samples': sorted(df['sample'].unique())
        }
    
    def _calculate_descriptive_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate and store descriptive statistics."""
        desc_stats = df[['TMB', 'OPD']].describe()
        print("Descriptive Statistics:")
        print(desc_stats)
        print()
        
        # Store results
        for substrate in ['TMB', 'OPD']:
            for stat in desc_stats.index:
                self.data_manager.add_result(
                    analysis_step='descriptive_stats',
                    substrate=substrate,
                    statistic=stat,
                    value=desc_stats.loc[stat, substrate]
                )
        
        # Additional statistics
        additional_stats = {}
        for substrate in ['TMB', 'OPD']:
            values = df[substrate]
            additional_stats[f'{substrate}_skewness'] = stats.skew(values)
            additional_stats[f'{substrate}_kurtosis'] = stats.kurtosis(values)
            additional_stats[f'{substrate}_cv_percent'] = (values.std() / values.mean()) * 100
            
            # Store additional stats
            self.data_manager.add_result(
                analysis_step='descriptive_stats',
                substrate=substrate,
                statistic='skewness',
                value=additional_stats[f'{substrate}_skewness']
            )
            self.data_manager.add_result(
                analysis_step='descriptive_stats',
                substrate=substrate,
                statistic='kurtosis',
                value=additional_stats[f'{substrate}_kurtosis']
            )
            self.data_manager.add_result(
                analysis_step='descriptive_stats',
                substrate=substrate,
                statistic='cv_percent',
                value=additional_stats[f'{substrate}_cv_percent']
            )
        
        print("Additional Statistics:")
        for key, value in additional_stats.items():
            print(f"{key}: {value:.4f}")
        
        return {**desc_stats.to_dict(), **additional_stats}
    
    def _create_distribution_plots(self, df: pd.DataFrame):
        """Create distribution plots for TMB and OPD."""
        print("Creating distribution plots...")
        
        # Create box plot
        plt.figure(figsize=(12, 8))
        
        # Prepare data for plotting
        plot_data = []
        for substrate in ['TMB', 'OPD']:
            for sample in df['sample'].unique():
                sample_data = df[df['sample'] == sample][substrate]
                for value in sample_data:
                    plot_data.append({
                        'Substrate': substrate,
                        'Sample': sample,
                        'Value': value
                    })
        
        plot_df = pd.DataFrame(plot_data)
        
        # Create box plot
        sns.boxplot(data=plot_df, x='Sample', y='Value', hue='Substrate', palette='viridis')
        plt.title('TMB and OPD Distribution by Sample', fontsize=16, fontweight='bold')
        plt.xlabel('Sample', fontsize=14)
        plt.ylabel('Activity Value', fontsize=14)
        plt.legend(title='Substrate', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Use the PlotManager's save_plot method
        self.plot_manager.save_plot('boxplot_TMB_OPD_distribution.png', data=plot_df)
    
    def _create_individual_distribution_plots(self, df: pd.DataFrame):
        """Create individual distribution plots with data export."""
        config = self.config.get_module_config('descriptive_stats')
        bins = config.get('histogram_bins', 20)
        alpha = config.get('distribution_alpha', 0.7)
        
        # Individual box plots for each substrate
        for substrate in ['TMB', 'OPD']:
            def plot_boxplot(ax, substrate=substrate):
                box_data = df.groupby('sample')[substrate].apply(list).reset_index()
                box_data_expanded = []
                for _, row in box_data.iterrows():
                    for value in row[substrate]:
                        box_data_expanded.append({'sample': row['sample'], substrate: value})
                box_df = pd.DataFrame(box_data_expanded)
                
                sns.boxplot(data=box_df, x='sample', y=substrate, ax=ax)
                ax.set_title(f'{substrate} Distribution by Sample')
                ax.set_ylabel(f'{substrate} Activity')
                ax.grid(True, alpha=0.3)
                return box_df
            
            # Save individual box plot with data
            box_data = df[['sample', substrate]].copy()
            self.plot_manager.save_single_plot(
                f'boxplot_{substrate.lower()}.png', 
                plot_boxplot, 
                data=box_data
            )
        
        # Individual histograms
        for substrate in ['TMB', 'OPD']:
            def plot_histogram(ax, substrate=substrate):
                values = df[substrate]
                
                # Histogram
                n, bins_edges, patches = ax.hist(values, bins=bins, density=True, 
                                                alpha=alpha, color=f'C{["TMB", "OPD"].index(substrate)}', 
                                                label=f'{substrate} Data')
                
                # Normal distribution overlay
                mu, sigma = values.mean(), values.std()
                x = np.linspace(values.min(), values.max(), 100)
                y = stats.norm.pdf(x, mu, sigma)
                ax.plot(x, y, 'r--', linewidth=2, label='Normal Fit')
                
                ax.set_xlabel(f'{substrate} Activity')
                ax.set_ylabel('Density')
                ax.set_title(f'{substrate} Distribution')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # Create data for export (separate histogram and normal fit data)
                hist_data = pd.DataFrame({
                    'bin_centers': (bins_edges[:-1] + bins_edges[1:]) / 2,
                    'density': n
                })
                
                normal_fit_data = pd.DataFrame({
                    'normal_fit_x': x,
                    'normal_fit_y': y
                })
                
                # Combine with appropriate indexing
                max_len = max(len(hist_data), len(normal_fit_data))
                hist_export = pd.DataFrame(index=range(max_len))
                
                # Add histogram data
                for col in hist_data.columns:
                    hist_export[col] = hist_data[col].reindex(range(max_len))
                
                # Add normal fit data  
                for col in normal_fit_data.columns:
                    hist_export[col] = normal_fit_data[col].reindex(range(max_len))
                    
                return hist_export
            
            # Save individual histogram with data
            self.plot_manager.save_single_plot(
                f'histogram_{substrate.lower()}.png', 
                plot_histogram
            )
    
    def _create_concentration_response_plots(self, df: pd.DataFrame):
        """Create concentration-response plots for both substrates."""
        print("Creating concentration-response plots...")
        
        for substrate in ['TMB', 'OPD']:
            # Calculate means and confidence intervals
            grouped = df.groupby(['sample', 'concentration_value'])[substrate].agg(['mean', 'std', 'count']).reset_index()
            grouped['se'] = grouped['std'] / np.sqrt(grouped['count'])
            grouped['ci_lower'] = grouped['mean'] - 1.96 * grouped['se']
            grouped['ci_upper'] = grouped['mean'] + 1.96 * grouped['se']
            
            # Create both linear and log scale versions
            for scale in ['linear', 'log']:
                plt.figure(figsize=(10, 8))
                
                palette = sns.color_palette("viridis", len(grouped['sample'].unique()))
                
                for i, (sample, group_data) in enumerate(grouped.groupby('sample')):
                    plt.plot(group_data['concentration_value'], group_data['mean'], 
                            marker='o', color=palette[i], label=sample, linewidth=2, markersize=6)
                    plt.fill_between(group_data['concentration_value'], 
                                   group_data['ci_lower'], group_data['ci_upper'], 
                                   color=palette[i], alpha=0.2)
                
                if scale == 'log':
                    plt.xscale('log')
                    plt.xlabel('Concentration (μM, log scale)', fontsize=14)
                else:
                    plt.xlabel('Concentration (μM)', fontsize=14)
                    
                plt.ylabel(f'{substrate} Activity', fontsize=14)
                plt.title(f'{substrate} Kinetic Curves ({scale.title()} Scale)', 
                         fontsize=16, fontweight='bold')
                plt.legend(fontsize=12, frameon=True, shadow=True)
                plt.grid(True, alpha=0.3)
                plt.tight_layout()
                
                # Save plot
                filepath = f"{self.data_manager.output_dir}/visualizations/kinetic_curves_{substrate}_{scale}.png"
                plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()
                
                print(f"✅ {substrate} kinetic curve ({scale}) saved: {filepath}")
    
    def _create_required_kinetic_plots(self, grouped: pd.DataFrame):
        """Create kinetic curve plots as specifically required: separate TMB and OPD plots."""
        for substrate in ['TMB', 'OPD']:
            def plot_kinetic_curve(ax, scale='log'):
                """Plot kinetic curves with specified scale."""
                palette = sns.color_palette("viridis", len(grouped['sample'].unique()))
                
                for i, (sample, group_data) in enumerate(grouped.groupby('sample')):
                    ax.plot(group_data['concentration_value'], group_data[f'{substrate}_mean'], 
                           marker='o', color=palette[i], label=sample, linewidth=2, markersize=6)
                    ax.fill_between(group_data['concentration_value'], 
                                   group_data[f'{substrate}_ci_lower'], 
                                   group_data[f'{substrate}_ci_upper'], 
                                   color=palette[i], alpha=0.2)
                
                if scale == 'log':
                    ax.set_xscale('log')
                    ax.set_xlabel('Concentration (μM, log scale)', fontsize=14)
                else:
                    ax.set_xlabel('Concentration (μM)', fontsize=14)
                    
                ax.set_ylabel(f'{substrate} Activity', fontsize=14)
                ax.set_title(f'{substrate} Kinetic Curves ({scale.title()} Scale)', 
                            fontsize=16, fontweight='bold')
                ax.legend(fontsize=12, frameon=True, shadow=True)
                ax.grid(True, alpha=0.3)
                ax.tick_params(labelsize=12)

            # Prepare data for export
            curve_data = grouped[['sample', 'concentration_value', 
                                f'{substrate}_mean', f'{substrate}_std', f'{substrate}_ci']].copy()
            curve_data = curve_data.rename(columns={
                f'{substrate}_mean': 'mean_activity',
                f'{substrate}_std': 'std_activity', 
                f'{substrate}_ci': 'ci_95'
            })

            # Create both scale versions
            self.plot_manager.create_dual_scale_plots(
                f'kinetic_curves_{substrate}', 
                plot_kinetic_curve,
                data=curve_data
            )
    
    def _create_individual_kinetic_plots(self, grouped: pd.DataFrame):
        """Create individual kinetic curve plots for each substrate."""
        for substrate in ['TMB', 'OPD']:
            def plot_kinetic_curve(ax, substrate=substrate):
                for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
                    sample_data = grouped[grouped['sample'] == sample]
                    if not sample_data.empty:
                        ax.errorbar(
                            sample_data['concentration_value'],
                            sample_data[f'{substrate}_mean'],
                            yerr=sample_data[f'{substrate}_ci'],
                            label=f'Sample {sample}',
                            marker='o',
                            capsize=5,
                            linewidth=2,
                            markersize=6
                        )
                
                ax.set_xscale('log')
                ax.set_xlabel('Concentration (μM)')
                ax.set_ylabel(f'{substrate} Activity')
                ax.set_title(f'{substrate} Kinetic Curves (Mean ± 95% CI)')
                ax.legend()
                ax.grid(True, alpha=0.3)
            
            # Prepare data for export
            curve_data = grouped[['sample', 'concentration_value', 
                                f'{substrate}_mean', f'{substrate}_std', f'{substrate}_ci']].copy()
            curve_data = curve_data.rename(columns={
                f'{substrate}_mean': 'mean_activity',
                f'{substrate}_std': 'std_activity', 
                f'{substrate}_ci': 'ci_95'
            })
            
            # Save individual plot with data
            self.plot_manager.save_single_plot(
                f'kinetic_curve_{substrate.lower()}.png',
                plot_kinetic_curve,
                data=curve_data
            )


class KineticCurveModule(AnalysisModule):
    """Dedicated module for kinetic curve analysis and visualization."""
    
    def get_module_name(self) -> str:
        return "Step 2: Kinetic Curve Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute kinetic curve analysis."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Generate detailed kinetic analysis
        curve_analysis = self._analyze_kinetic_curves(df)
        self._create_detailed_kinetic_plots(df)
        
        return curve_analysis
    
    def _analyze_kinetic_curves(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze kinetic curves for each sample-substrate combination."""
        results = {}
        
        for substrate in ['TMB', 'OPD']:
            results[substrate] = {}
            
            for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
                sample_data = df[df['sample'] == sample]
                
                # Group by concentration
                conc_data = sample_data.groupby('concentration_value')[substrate].agg([
                    'mean', 'std', 'count'
                ]).reset_index()
                
                # Calculate slope between adjacent points (approximation of velocity)
                if len(conc_data) > 1:
                    slopes = []
                    for i in range(1, len(conc_data)):
                        dx = np.log10(conc_data.iloc[i]['concentration_value']) - \
                             np.log10(conc_data.iloc[i-1]['concentration_value'])
                        dy = conc_data.iloc[i]['mean'] - conc_data.iloc[i-1]['mean']
                        slopes.append(dy / dx if dx != 0 else 0)
                    
                    results[substrate][sample] = {
                        'max_activity': conc_data['mean'].max(),
                        'min_activity': conc_data['mean'].min(),
                        'activity_range': conc_data['mean'].max() - conc_data['mean'].min(),
                        'avg_slope': np.mean(slopes) if slopes else 0,
                        'concentration_range': [
                            conc_data['concentration_value'].min(),
                            conc_data['concentration_value'].max()
                        ]
                    }
                    
                    # Store detailed results
                    for metric, value in results[substrate][sample].items():
                        if isinstance(value, list):
                            self.data_manager.add_result(
                                analysis_step='kinetic_curve_analysis',
                                substrate=substrate,
                                sample=sample,
                                metric=f'{metric}_min',
                                value=value[0]
                            )
                            self.data_manager.add_result(
                                analysis_step='kinetic_curve_analysis',
                                substrate=substrate,
                                sample=sample,
                                metric=f'{metric}_max',
                                value=value[1]
                            )
                        else:
                            self.data_manager.add_result(
                                analysis_step='kinetic_curve_analysis',
                                substrate=substrate,
                                sample=sample,
                                metric=metric,
                                value=value
                            )
        
        return results
    
    def _create_detailed_kinetic_plots(self, df: pd.DataFrame):
        """Create detailed kinetic curve plots with individual replicates."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        samples = ['A', 'B', 'C', 'D', 'E', 'F']
        
        for idx, sample in enumerate(samples):
            ax = axes[idx]
            sample_data = df[df['sample'] == sample]
            
            for substrate in ['TMB', 'OPD']:
                # Plot individual replicates
                for parallel in sample_data['Parallel'].unique():
                    parallel_data = sample_data[sample_data['Parallel'] == parallel]
                    ax.scatter(parallel_data['concentration_value'], 
                             parallel_data[substrate],
                             alpha=0.6, s=30, label=f'{substrate}_{parallel}')
                
                # Plot means
                means = sample_data.groupby('concentration_value')[substrate].mean()
                ax.plot(means.index, means.values, 'o-', linewidth=2, 
                       markersize=8, label=f'{substrate} Mean')
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM)')
            ax.set_ylabel('Activity')
            ax.set_title(f'Sample {sample} - Detailed View')
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.plot_manager.save_plot('detailed_kinetic_curves_by_sample.png')
