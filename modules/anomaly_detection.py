#!/usr/bin/env python3
"""
Anomaly Detection Module
=======================

This module handles anomaly detection using Isolation Forest algorithm
to identify potential outliers in the enzyme kinetics data.
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

from .base import AnalysisModule


class AnomalyDetectionModule(AnalysisModule):
    """Module for anomaly detection analysis."""
    
    def get_module_name(self) -> str:
        return "Step 9: Anomaly Detection Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute anomaly detection analysis."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Prepare features for anomaly detection
        anomaly_features = self._prepare_anomaly_features(df)
        
        # Perform anomaly detection
        anomaly_results = self._perform_anomaly_detection(anomaly_features, df)
        
        # Create visualizations
        self._create_anomaly_visualizations(anomaly_results, df)
        
        return anomaly_results
    
    def _prepare_anomaly_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for anomaly detection."""
        print("Preparing features for anomaly detection...")
        
        # Use multiple feature combinations for robust anomaly detection
        anomaly_features = df[['TMB', 'OPD', 'concentration_value']].copy()
        
        # Add derived features
        anomaly_features['TMB_OPD_ratio'] = anomaly_features['TMB'] / anomaly_features['OPD']
        anomaly_features['log_concentration'] = np.log10(anomaly_features['concentration_value'])
        anomaly_features['TMB_OPD_product'] = anomaly_features['TMB'] * anomaly_features['OPD']
        anomaly_features['TMB_squared'] = anomaly_features['TMB'] ** 2
        anomaly_features['OPD_squared'] = anomaly_features['OPD'] ** 2
        
        print(f"Anomaly detection features shape: {anomaly_features.shape}")
        
        return anomaly_features
    
    def _perform_anomaly_detection(self, features: pd.DataFrame, original_df: pd.DataFrame) -> Dict[str, Any]:
        """Perform anomaly detection using Isolation Forest."""
        print("Performing anomaly detection...")
        
        # Get anomaly detection configuration
        config = self.config.get_module_config('anomaly_detection')
        contamination_rate = config.get('contamination_rate', 0.1)
        algorithm = config.get('algorithm', 'isolation_forest')
        random_state = self.config.get('random_state', 42)
        
        # Standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # Fit Isolation Forest
        iso_forest = IsolationForest(
            contamination=contamination_rate, 
            random_state=random_state,
            n_estimators=100
        )
        
        # Fit and predict
        anomaly_labels = iso_forest.fit_predict(features_scaled)
        anomaly_scores = iso_forest.decision_function(features_scaled)
        
        # Identify outliers
        outliers_mask = anomaly_labels == -1
        n_outliers = np.sum(outliers_mask)
        
        print(f"Detected {n_outliers} outliers out of {len(features)} data points ({n_outliers/len(features)*100:.1f}%)")
        
        # Store anomaly detection results
        for i, (score, label) in enumerate(zip(anomaly_scores, anomaly_labels)):
            self.data_manager.add_result(
                analysis_step='anomaly_detection_scores',
                data_point=i,
                anomaly_score=score,
                is_outlier=label == -1,
                sample=original_df.iloc[i]['sample'],
                concentration=original_df.iloc[i]['concentration_value'],
                parallel=original_df.iloc[i]['Parallel'],
                TMB=original_df.iloc[i]['TMB'],
                OPD=original_df.iloc[i]['OPD']
            )
        
        # Analyze outliers by sample and concentration
        outlier_analysis = self._analyze_outliers(original_df, outliers_mask)
        
        results = {
            'anomaly_scores': anomaly_scores,
            'anomaly_labels': anomaly_labels,
            'outliers_mask': outliers_mask,
            'n_outliers': n_outliers,
            'contamination_rate': contamination_rate,
            'outlier_analysis': outlier_analysis,
            'model': iso_forest,
            'scaler': scaler
        }
        
        return results
    
    def _analyze_outliers(self, df: pd.DataFrame, outliers_mask: np.ndarray) -> Dict[str, Any]:
        """Analyze the distribution of outliers across samples and concentrations."""
        outlier_data = df[outliers_mask].copy()
        
        analysis = {
            'outliers_by_sample': outlier_data['sample'].value_counts().to_dict(),
            'outliers_by_concentration': outlier_data['concentration_value'].value_counts().to_dict(),
            'outliers_by_parallel': outlier_data['Parallel'].value_counts().to_dict(),
            'outlier_statistics': {
                'TMB_mean': outlier_data['TMB'].mean(),
                'TMB_std': outlier_data['TMB'].std(),
                'OPD_mean': outlier_data['OPD'].mean(),
                'OPD_std': outlier_data['OPD'].std(),
            }
        }
        
        print("\nOutlier Analysis:")
        print(f"Outliers by sample: {analysis['outliers_by_sample']}")
        print(f"Outliers by concentration: {analysis['outliers_by_concentration']}")
        
        return analysis
    
    def _create_anomaly_visualizations(self, anomaly_results: Dict[str, Any], df: pd.DataFrame):
        """Create anomaly detection visualization plots."""
        
        # Create the main anomaly plot as required
        self._create_required_anomaly_plot(anomaly_results, df)
        
        # Create additional anomaly analysis plots
        self._create_additional_anomaly_plots(anomaly_results, df)
    
    def _create_required_anomaly_plot(self, anomaly_results: Dict[str, Any], df: pd.DataFrame):
        """Create anomaly detection plot as specifically required."""
        def plot_anomaly_detection(ax):
            anomaly_scores = anomaly_results['anomaly_scores']
            outliers_mask = anomaly_results['outliers_mask']
            
            # Create scatter plot of concentration vs TMB, colored by anomaly score
            scatter = ax.scatter(df['concentration_value'], df['TMB'], 
                               c=anomaly_scores, cmap='RdYlBu', 
                               alpha=0.7, s=60, edgecolors='black', linewidth=0.5)
            
            # Highlight outliers with red X marks
            if np.any(outliers_mask):
                ax.scatter(df.loc[outliers_mask, 'concentration_value'],
                          df.loc[outliers_mask, 'TMB'],
                          c='red', s=200, marker='x', linewidth=3, 
                          label=f'Outliers (n={np.sum(outliers_mask)})')
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('TMB Activity', fontsize=14)
            ax.set_title('Anomaly Detection: Concentration vs TMB Activity', 
                        fontsize=16, fontweight='bold')
            
            # Add colorbar
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('Anomaly Score', fontsize=12)
            
            if np.any(outliers_mask):
                ax.legend(fontsize=12)
            
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        # Prepare data for export
        anomaly_data = df.copy()
        anomaly_data['anomaly_score'] = anomaly_results['anomaly_scores']
        anomaly_data['is_outlier'] = anomaly_results['outliers_mask']
        
        self.plot_manager.save_single_plot('anomaly_detection_plot.png', 
                                         plot_anomaly_detection, data=anomaly_data)
    
    def _create_additional_anomaly_plots(self, anomaly_results: Dict[str, Any], df: pd.DataFrame):
        """Create additional anomaly detection analysis plots."""
        
        # TMB vs OPD anomaly plot
        def plot_tmb_opd_anomaly(ax):
            anomaly_scores = anomaly_results['anomaly_scores']
            outliers_mask = anomaly_results['outliers_mask']
            
            # Scatter plot of TMB vs OPD
            scatter = ax.scatter(df['TMB'], df['OPD'], 
                               c=anomaly_scores, cmap='RdYlBu', 
                               alpha=0.7, s=60, edgecolors='black', linewidth=0.5)
            
            # Highlight outliers
            if np.any(outliers_mask):
                ax.scatter(df.loc[outliers_mask, 'TMB'],
                          df.loc[outliers_mask, 'OPD'],
                          c='red', s=200, marker='x', linewidth=3, 
                          label=f'Outliers (n={np.sum(outliers_mask)})')
            
            ax.set_xlabel('TMB Activity', fontsize=14)
            ax.set_ylabel('OPD Activity', fontsize=14)
            ax.set_title('Anomaly Detection: TMB vs OPD Activity', 
                        fontsize=16, fontweight='bold')
            
            plt.colorbar(scatter, ax=ax, label='Anomaly Score')
            
            if np.any(outliers_mask):
                ax.legend(fontsize=12)
            
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        tmb_opd_data = df[['TMB', 'OPD']].copy()
        tmb_opd_data['anomaly_score'] = anomaly_results['anomaly_scores']
        tmb_opd_data['is_outlier'] = anomaly_results['outliers_mask']
        
        self.plot_manager.save_single_plot('anomaly_tmb_vs_opd.png', 
                                         plot_tmb_opd_anomaly, data=tmb_opd_data)
        
        # Anomaly score distribution
        def plot_anomaly_distribution(ax):
            anomaly_scores = anomaly_results['anomaly_scores']
            outliers_mask = anomaly_results['outliers_mask']
            
            # Histogram of anomaly scores
            ax.hist(anomaly_scores, bins=20, alpha=0.7, color='skyblue', 
                   edgecolor='black', label='All Data')
            
            # Highlight outlier scores
            if np.any(outliers_mask):
                outlier_scores = anomaly_scores[outliers_mask]
                ax.hist(outlier_scores, bins=20, alpha=0.9, color='red', 
                       edgecolor='darkred', label='Outliers')
            
            ax.set_xlabel('Anomaly Score', fontsize=14)
            ax.set_ylabel('Frequency', fontsize=14)
            ax.set_title('Distribution of Anomaly Scores', fontsize=16, fontweight='bold')
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        score_dist_data = pd.DataFrame({
            'anomaly_score': anomaly_results['anomaly_scores'],
            'is_outlier': anomaly_results['outliers_mask']
        })
        
        self.plot_manager.save_single_plot('anomaly_score_distribution.png', 
                                         plot_anomaly_distribution, data=score_dist_data)
        
        # Outliers by sample and concentration
        def plot_outlier_summary(ax):
            outlier_analysis = anomaly_results['outlier_analysis']
            
            # Create bar plot of outliers by sample
            samples = list(outlier_analysis['outliers_by_sample'].keys())
            counts = list(outlier_analysis['outliers_by_sample'].values())
            
            bars = ax.bar(samples, counts, color='coral', alpha=0.8, edgecolor='black')
            
            # Add value labels on bars
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{count}', ha='center', va='bottom', fontsize=12, fontweight='bold')
            
            ax.set_xlabel('Sample', fontsize=14)
            ax.set_ylabel('Number of Outliers', fontsize=14)
            ax.set_title('Outliers by Sample', fontsize=16, fontweight='bold')
            ax.tick_params(labelsize=12)
            ax.grid(True, alpha=0.3, axis='y')
        
        outlier_summary_data = pd.DataFrame({
            'sample': list(anomaly_results['outlier_analysis']['outliers_by_sample'].keys()),
            'outlier_count': list(anomaly_results['outlier_analysis']['outliers_by_sample'].values())
        })
        
        self.plot_manager.save_single_plot('outliers_by_sample.png', 
                                         plot_outlier_summary, data=outlier_summary_data)
