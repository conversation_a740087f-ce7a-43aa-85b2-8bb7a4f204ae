#!/usr/bin/env python3
"""
MANOVA Statistical Analysis Module
=================================

This module handles Multivariate Analysis of Variance (MANOVA) testing
to examine the effects of sample and concentration on TMB and OPD responses.
"""

from typing import Dict, Any
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import statsmodels.api as sm
from statsmodels.multivariate.manova import MANOVA

from .base import AnalysisModule


class MANOVAAnalysisModule(AnalysisModule):
    """Module for MANOVA statistical analysis and interaction plotting."""
    
    def get_module_name(self) -> str:
        return "Step 4: MANOVA Statistical Analysis"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute MANOVA analysis and create interaction plots."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Perform MANOVA
        manova_results = self._perform_manova(df)
        
        # Create interaction plots
        self._create_interaction_plots(df)
        
        return manova_results
    
    def _perform_manova(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform MANOVA with sample and concentration factors."""
        print("Performing MANOVA Analysis...")
        
        # Prepare data for MANOVA
        manova_data = df[['sample', 'concentration_value', 'TMB', 'OPD']].copy()
        
        # Convert categorical variables
        manova_data['sample_cat'] = pd.Categorical(manova_data['sample'])
        manova_data['concentration_cat'] = pd.Categorical(manova_data['concentration_value'])
        
        results = {}
        
        try:
            # Perform MANOVA using statsmodels
            manova = MANOVA.from_formula(
                'TMB + OPD ~ sample_cat + concentration_cat + sample_cat:concentration_cat',
                data=manova_data
            )
            manova_results = manova.mv_test()
            
            print("MANOVA Results:")
            print(manova_results)
            
            # Store MANOVA results
            for effect in manova_results.results.keys():
                stats_dict = manova_results.results[effect]
                if "Pillai's trace" in stats_dict:
                    pillai_stats = stats_dict["Pillai's trace"]
                    
                    effect_result = {
                        'effect': effect,
                        'test_statistic': 'Pillai_trace',
                        'value': pillai_stats['Value'],
                        'f_statistic': pillai_stats['F Value'],
                        'p_value': pillai_stats['Pr > F'],
                        'num_df': pillai_stats['Num DF'],
                        'den_df': pillai_stats['Den DF']
                    }
                    
                    results[effect] = effect_result
                    
                    # Store in data manager
                    self.data_manager.add_result(
                        analysis_step='manova_results',
                        **effect_result
                    )
            
            # Additional effect size calculations
            results['effect_sizes'] = self._calculate_effect_sizes(manova_data)
            
        except Exception as e:
            print(f"MANOVA analysis failed: {e}")
            results['error'] = str(e)
        
        return results
    
    def _calculate_effect_sizes(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate effect sizes for the factors."""
        effect_sizes = {}
        
        # Calculate eta-squared for each factor
        for substrate in ['TMB', 'OPD']:
            # Sample effect
            sample_groups = [df[df['sample'] == sample][substrate] for sample in df['sample'].unique()]
            f_stat, p_val = stats.f_oneway(*sample_groups)
            
            # Calculate eta-squared
            ss_between = sum([len(group) * (np.mean(group) - np.mean(df[substrate]))**2 for group in sample_groups])
            ss_total = np.sum((df[substrate] - np.mean(df[substrate]))**2)
            eta_squared = ss_between / ss_total if ss_total > 0 else 0
            
            effect_sizes[f'{substrate}_sample_eta_squared'] = eta_squared
            
            # Concentration effect
            conc_groups = [df[df['concentration_value'] == conc][substrate] for conc in df['concentration_value'].unique()]
            f_stat_conc, p_val_conc = stats.f_oneway(*conc_groups)
            
            # Calculate eta-squared for concentration
            ss_between_conc = sum([len(group) * (np.mean(group) - np.mean(df[substrate]))**2 for group in conc_groups])
            eta_squared_conc = ss_between_conc / ss_total if ss_total > 0 else 0
            
            effect_sizes[f'{substrate}_concentration_eta_squared'] = eta_squared_conc
        
        return effect_sizes
    
    def _create_interaction_plots(self, df: pd.DataFrame):
        """Create interaction plots as specifically required."""
        
        # Create the main interaction plot as required
        self._create_required_interaction_plot(df)
        
        # Create additional detailed interaction plots
        self._create_detailed_interaction_plots(df)
    
    def _create_required_interaction_plot(self, df: pd.DataFrame):
        """Create MANOVA interaction plot as specifically required."""
        def plot_interaction(ax):
            # Calculate means for interaction plot
            interaction_means = df.groupby(['sample', 'concentration_value']).agg({
                'TMB': 'mean',
                'OPD': 'mean'
            }).reset_index()
            
            # Create separate lines for TMB and OPD
            samples = ['A', 'B', 'C', 'D', 'E', 'F']
            colors = plt.cm.Set1(np.linspace(0, 1, len(samples)))
            
            # Plot TMB lines (solid)
            for i, sample in enumerate(samples):
                sample_data = interaction_means[interaction_means['sample'] == sample]
                ax.plot(sample_data['concentration_value'], sample_data['TMB'], 
                       'o-', color=colors[i], linewidth=2, markersize=8, 
                       label=f'TMB Sample {sample}', alpha=0.8)
            
            # Plot OPD lines (dashed)
            for i, sample in enumerate(samples):
                sample_data = interaction_means[interaction_means['sample'] == sample]
                ax.plot(sample_data['concentration_value'], sample_data['OPD'], 
                       'o--', color=colors[i], linewidth=2, markersize=8, 
                       label=f'OPD Sample {sample}', alpha=0.8)
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('Activity', fontsize=14)
            ax.set_title('MANOVA Interaction Effects: Sample × Concentration', 
                        fontsize=16, fontweight='bold')
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        # Prepare data for export
        interaction_data = df.groupby(['sample', 'concentration_value']).agg({
            'TMB': ['mean', 'std'],
            'OPD': ['mean', 'std']
        }).reset_index()
        
        # Flatten column names
        interaction_data.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                                  for col in interaction_data.columns]
        
        self.plot_manager.save_single_plot('manova_interaction_plot.png', plot_interaction, 
                                         data=interaction_data)
    
    def _create_detailed_interaction_plots(self, df: pd.DataFrame):
        """Create detailed interaction plots for better understanding."""
        
        # Separate plots for TMB and OPD
        for substrate in ['TMB', 'OPD']:
            def plot_substrate_interaction(ax, substrate=substrate):
                interaction_means = df.groupby(['sample', 'concentration_value'])[substrate].mean().reset_index()
                
                samples = ['A', 'B', 'C', 'D', 'E', 'F']
                colors = plt.cm.Set1(np.linspace(0, 1, len(samples)))
                
                for i, sample in enumerate(samples):
                    sample_data = interaction_means[interaction_means['sample'] == sample]
                    ax.plot(sample_data['concentration_value'], sample_data[substrate], 
                           'o-', color=colors[i], linewidth=3, markersize=10, 
                           label=f'Sample {sample}', alpha=0.8)
                
                ax.set_xscale('log')
                ax.set_xlabel('Concentration (μM)', fontsize=14)
                ax.set_ylabel(f'{substrate} Activity', fontsize=14)
                ax.set_title(f'{substrate} Interaction Plot: Sample × Concentration', 
                            fontsize=16, fontweight='bold')
                ax.legend(fontsize=12)
                ax.grid(True, alpha=0.3)
                ax.tick_params(labelsize=12)
            
            # Prepare data
            substrate_data = df.groupby(['sample', 'concentration_value']).agg({
                substrate: ['mean', 'std', 'count']
            }).reset_index()
            
            substrate_data.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                                    for col in substrate_data.columns]
            
            filename = f'interaction_plot_{substrate}.png'
            self.plot_manager.save_single_plot(filename, plot_substrate_interaction, 
                                             data=substrate_data)
        
        # Combined interaction plot with confidence intervals
        self._create_interaction_with_ci(df)
    
    def _create_interaction_with_ci(self, df: pd.DataFrame):
        """Create interaction plot with confidence intervals."""
        def plot_interaction_ci(ax):
            # Calculate means and confidence intervals
            grouped = df.groupby(['sample', 'concentration_value']).agg({
                'TMB': ['mean', 'std', 'count'],
                'OPD': ['mean', 'std', 'count']
            }).reset_index()
            
            # Flatten column names
            grouped.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                              for col in grouped.columns]
            
            # Calculate 95% CI
            confidence_level = self.config.get('confidence_level', 0.95)
            for substrate in ['TMB', 'OPD']:
                grouped[f'{substrate}_ci'] = (
                    grouped[f'{substrate}_std'] / np.sqrt(grouped[f'{substrate}_count']) * 
                    stats.t.ppf((1 + confidence_level) / 2, grouped[f'{substrate}_count'] - 1)
                )
            
            samples = ['A', 'B', 'C', 'D', 'E', 'F']
            colors = plt.cm.Set1(np.linspace(0, 1, len(samples)))
            
            for i, sample in enumerate(samples):
                sample_data = grouped[grouped['sample'] == sample]
                
                # Plot TMB with CI
                ax.errorbar(sample_data['concentration_value'], sample_data['TMB_mean'],
                           yerr=sample_data['TMB_ci'], fmt='o-', color=colors[i], 
                           linewidth=2, markersize=8, capsize=5, capthick=2,
                           label=f'TMB Sample {sample}', alpha=0.7)
            
            ax.set_xscale('log')
            ax.set_xlabel('Concentration (μM)', fontsize=14)
            ax.set_ylabel('TMB Activity', fontsize=14)
            ax.set_title('TMB Interaction Plot with 95% Confidence Intervals', 
                        fontsize=16, fontweight='bold')
            ax.legend(fontsize=10, ncol=2)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=12)
        
        # Prepare data for export
        ci_data = df.groupby(['sample', 'concentration_value']).agg({
            'TMB': ['mean', 'std', 'count'],
            'OPD': ['mean', 'std', 'count']
        }).reset_index()
        
        ci_data.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                          for col in ci_data.columns]
        
        self.plot_manager.save_single_plot('interaction_plot_with_ci.png', plot_interaction_ci, 
                                         data=ci_data)
