#!/usr/bin/env python3
"""
Base Classes and Utilities for Enzyme Kinetics Analysis
======================================================

This module provides the foundation classes and utilities used across
all analysis modules.
"""

import os
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional, Callable
from abc import ABC, abstractmethod

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Configure warnings and plotting style [[memory:4927745]]
warnings.filterwarnings('ignore')
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12


class AnalysisConfig:
    """Enhanced configuration management with module-specific parameters."""
    
    def __init__(self):
        # Global configuration [[memory:5198663]]
        self.config = {
            'data_file': '/Users/<USER>/Documents/nie/sample0513.csv',
            'output_base_dir': '/Users/<USER>/Documents/nie/outputs',
            'confidence_level': 0.95,
            'random_state': 42,
            'figure_dpi': 300,
        }
        
        # Module-specific configurations
        self.module_configs = {
            'descriptive_stats': {
                'histogram_bins': 20,
                'distribution_alpha': 0.7,
                'show_normal_overlay': True,
                'boxplot_showfliers': True
            },
            'quality_control': {
                'cv_threshold': 15.0,
                'correlation_method': 'pearson',  # pearson, spearman
                'show_regression_line': True,
                'highlight_outliers': True
            },
            'kinetic_modeling': {
                'max_iterations': 5000,
                'initial_guess_method': 'auto',  # auto, manual
                'confidence_interval': 0.95,
                'optimization_method': 'lm'  # lm, trf, dogbox
            },
            'machine_learning': {
                # === 梯度提升分类器参数 ===
                'n_estimators': 100,          # 决策树数量，增加可提高性能但训练时间更长
                'learning_rate': 0.1,         # 学习率，0.01-0.3，越小需要更多树
                'max_depth': 3,               # 树的最大深度，2-8，防止过拟合
                'min_samples_split': 2,       # 内部节点分割所需最小样本数
                'min_samples_leaf': 1,        # 叶节点最小样本数
                'subsample': 1.0,             # 采样比例，0.8-1.0，小于1可防止过拟合
                
                # === 交叉验证参数 ===
                'cv_folds': 3,                # 交叉验证折数，小数据集用3-5
                'test_size': 0.2,             # 测试集比例
                
                # === 特征工程参数 ===
                'feature_importance_threshold': 0.01,  # 特征重要性阈值
                'feature_selection_method': 'all',     # 特征选择：'all', 'top_k', 'threshold'
                'n_top_features': 10,         # 选择前N个重要特征
                
                # === 数据预处理参数 ===
                'scaling_method': 'standard', # 'standard', 'minmax', 'robust'
                'handle_imbalance': False,    # 是否处理类别不平衡
                'balance_strategy': 'SMOTE',  # 'SMOTE', 'RandomOverSampler', 'weights'
                
                # === 神经网络参数 (如果使用) ===
                'use_neural_network': True,   # 是否尝试训练神经网络
                'nn_hidden_layers': [64, 32], # 隐藏层神经元数量
                'nn_dropout_rate': 0.3,       # Dropout比例，防止过拟合
                'nn_activation': 'relu',      # 激活函数：'relu', 'tanh', 'sigmoid'
                'nn_optimizer': 'adam',       # 优化器：'adam', 'sgd', 'rmsprop'
                'nn_epochs': 100,             # 训练轮数
                'nn_batch_size': 4,           # 批次大小，小数据集用2-8
                'nn_validation_split': 0.2,  # 验证集比例
                'nn_early_stopping': True,   # 是否使用早停
                'nn_patience': 15,            # 早停耐心值
                
                # === 1D-CNN参数 ===
                'cnn_filters': [32, 16],      # 卷积层滤波器数量
                'cnn_kernel_size': 2,         # 卷积核大小
                'cnn_pool_size': 2,           # 池化层大小
                'cnn_dense_units': 64,        # 全连接层单元数
                'cnn_dropout': 0.3,           # Dropout比例
            },
            'clustering': {
                # === 层次聚类参数 ===
                'n_clusters': 3,              # 聚类数量，根据业务需求调整
                'linkage_method': 'ward',     # 连接方法：ward(最小方差), complete(最大距离), average(平均距离)
                'distance_metric': 'euclidean',  # 距离度量：euclidean, manhattan, cosine
                'min_cluster_size': 1,        # 最小聚类大小
                
                # === t-SNE降维参数 ===
                'tsne_n_components': 2,       # 降维后维度，通常为2或3用于可视化
                'tsne_perplexity': 3,         # 困惑度，5-50，小数据集用较小值
                'tsne_learning_rate': 200,   # 学习率，10-1000，可以尝试'auto'
                'tsne_n_iter': 1000,          # 最大迭代次数，默认1000
                'tsne_init': 'random',        # 初始化方法：'random', 'pca'
                'tsne_metric': 'euclidean',   # 距离度量
                'tsne_early_exaggeration': 12.0,  # 早期夸大因子
                'tsne_min_grad_norm': 1e-7,   # 最小梯度范数
                
                # === PCA降维参数 ===
                'use_pca_preprocessing': True,  # 是否在t-SNE前先用PCA降维
                'pca_n_components': 0.95,     # PCA保留的方差比例或组件数
                'pca_whiten': False,          # 是否白化数据
                
                # === K-means聚类参数 (备选) ===
                'kmeans_n_init': 10,          # K-means初始化次数
                'kmeans_max_iter': 300,       # 最大迭代次数
                'kmeans_tol': 1e-4,           # 收敛容忍度
                'kmeans_algorithm': 'auto',   # 算法：'auto', 'full', 'elkan'
            },
            'anomaly_detection': {
                'contamination_rate': 0.1,
                'algorithm': 'isolation_forest',  # isolation_forest, local_outlier_factor
                'n_neighbors': 20
            },
            'visualization': {
                'single_plot_size': (10, 8),  # Increased from (8, 6)
                'combined_plot_size': (20, 16),  # Increased from (12, 8) 
                'subplot_size_per_plot': (8, 6),  # New: individual subplot size
                'font_size': 14,
                'dpi': 300,
                'color_palette': 'husl',
                'grid_alpha': 0.3,
                'save_individual_plots': True,
                'show_plots': False,  # New: disable plot display
                'tight_layout_pad': 3.0,  # Increased padding
                'subplot_adjust': {
                    'left': 0.06,
                    'bottom': 0.06, 
                    'right': 0.94,
                    'top': 0.94,
                    'wspace': 0.4,  # Increased spacing
                    'hspace': 0.4
                }
            }
        }
    
    def update(self, **kwargs):
        """Update global configuration parameters."""
        self.config.update(kwargs)
    
    def update_module_config(self, module_name: str, **kwargs):
        """Update module-specific configuration."""
        if module_name not in self.module_configs:
            self.module_configs[module_name] = {}
        self.module_configs[module_name].update(kwargs)
    
    def get(self, key: str, default=None):
        """Get global configuration value."""
        return self.config.get(key, default)
    
    def get_module_config(self, module_name: str, key: str = None, default=None):
        """Get module-specific configuration."""
        module_config = self.module_configs.get(module_name, {})
        if key is None:
            return module_config
        return module_config.get(key, default)
    
    def get_all_module_configs(self):
        """Get all module configurations."""
        return self.module_configs.copy()
    
    def save_config(self, filepath: str):
        """Save configuration to file."""
        import json
        config_data = {
            'global': self.config,
            'modules': self.module_configs
        }
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def load_config(self, filepath: str):
        """Load configuration from file."""
        import json
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            self.config.update(config_data.get('global', {}))
            self.module_configs.update(config_data.get('modules', {}))
            return True
        except Exception as e:
            print(f"Failed to load config: {e}")
            return False


class DataManager:
    """Manages data loading, preprocessing, and storage."""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.df = None
        self.timestamp = datetime.now().strftime("%Y%m%d%H%M")
        self.output_dir = f"{config.get('output_base_dir')}/{self.timestamp}"
        self.results = []
        
    def create_output_directories(self):
        """Create timestamped output directories (simplified structure)."""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(f"{self.output_dir}/visualizations", exist_ok=True)
        os.makedirs(f"{self.output_dir}/data_exports", exist_ok=True)
        # 不再创建空的logs和metadata文件夹
        
    def load_data(self) -> pd.DataFrame:
        """Load and preprocess the enzyme kinetics data."""
        if self.df is None:
            self.df = pd.read_csv(self.config.get('data_file'))
            # Convert concentration from string to float
            self.df['concentration_value'] = self.df['concentration'].str.extract(r'(\d+)').astype(float)
        return self.df
    
    def add_result(self, analysis_step: str, **kwargs):
        """Add analysis result to the results collection."""
        result = {'analysis_step': analysis_step, **kwargs}
        self.results.append(result)
    
    def save_results_to_csv(self, filename: str = "analysis_results.csv"):
        """Save all numerical results to CSV file."""
        if self.results:
            results_df = pd.DataFrame(self.results)
            results_file = f"/Users/<USER>/Documents/nie/{filename}"
            results_df.to_csv(results_file, index=False)
            print(f"Results saved to: {results_file}")
            return results_file
        return None


class PlotManager:
    """Enhanced plot creation and saving with data export."""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        
        # 从配置获取可视化参数
        viz_config = data_manager.config.get_module_config('visualization')
        self.single_plot_size = viz_config.get('single_plot_size', (8, 6))
        self.combined_plot_size = viz_config.get('combined_plot_size', (12, 8))
        self.single_plot_dpi = viz_config.get('dpi', 300)
        self.font_size = viz_config.get('font_size', 14)
        self.tight_layout_pad = viz_config.get('tight_layout_pad', 2.0)
        self.subplot_adjust = viz_config.get('subplot_adjust', {})
        
    def configure_single_plot(self):
        """Configure matplotlib for single plot output."""
        plt.rcParams.update({
            'figure.figsize': self.single_plot_size,
            'figure.dpi': self.single_plot_dpi,
            'font.size': self.font_size,
            'axes.titlesize': self.font_size + 2,
            'axes.labelsize': self.font_size,
            'xtick.labelsize': self.font_size - 2,
            'ytick.labelsize': self.font_size - 2,
            'legend.fontsize': self.font_size - 2
        })
        
        def save_plot(self, filename: str, show: bool = False, data: pd.DataFrame = None, 
                     use_tight_layout: bool = True):
            """Save current plot with standardized naming and optional data export."""
            
            # 应用布局调整
            if use_tight_layout:
                try:
                    plt.tight_layout(pad=self.tight_layout_pad)
                except:
                    # 如果tight_layout失败，使用subplots_adjust
                    if self.subplot_adjust:
                        plt.subplots_adjust(**self.subplot_adjust)
            
            filepath = f"{self.data_manager.output_dir}/visualizations/{filename}"
            plt.savefig(filepath, dpi=self.data_manager.config.get('figure_dpi'), 
                       bbox_inches='tight', facecolor='white', edgecolor='none')
            if show:
                plt.show()
            plt.close()
            print(f"Plot saved: {filename}")
            
            # Save corresponding data if provided
            if data is not None:
                self.save_plot_data(filename, data)
    
    def save_single_plot(self, filename: str, plot_func: Callable, data: pd.DataFrame = None, 
                        show: bool = True, **kwargs):
        """Save a single plot with optimized settings."""
        self.configure_single_plot()
        
        fig, ax = plt.subplots(figsize=self.single_plot_size)
        
        # Execute the plotting function
        plot_func(ax, **kwargs)
        
        # Save plot
        filepath = f"{self.data_manager.output_dir}/visualizations/{filename}"
        plt.savefig(filepath, dpi=self.single_plot_dpi, bbox_inches='tight')
        
        if show:
            plt.show()
        plt.close()
        
        print(f"Single plot saved: {filename}")
        
        # Save corresponding data
        if data is not None:
            self.save_plot_data(filename, data)
    
    def save_plot_data(self, plot_filename: str, data: pd.DataFrame):
        """Save data corresponding to a plot."""
        # Create data filename from plot filename
        data_filename = plot_filename.replace('.png', '_data.csv')
        data_filepath = f"{self.data_manager.output_dir}/visualizations/{data_filename}"
        
        data.to_csv(data_filepath, index=False)
        print(f"Plot data saved: {data_filename}")
        
    def create_plot_series(self, base_filename: str, plot_functions: List[Callable], 
                          data_list: List[pd.DataFrame] = None, **kwargs):
        """Create a series of individual plots."""
        if data_list is None:
            data_list = [None] * len(plot_functions)
            
        for i, (plot_func, data) in enumerate(zip(plot_functions, data_list)):
            filename = f"{base_filename}_{i+1:02d}.png"
            self.save_single_plot(filename, plot_func, data, **kwargs)
    
    def create_combined_plot(self, nrows: int, ncols: int, figsize: tuple = None):
        """Create a combined plot with specified layout."""
        if figsize is None:
            figsize = self.combined_plot_size
        
        fig, axes = plt.subplots(nrows, ncols, figsize=figsize)
        
        # 设置字体大小
        plt.rcParams.update({
            'font.size': self.font_size,
            'axes.titlesize': self.font_size + 2,
            'axes.labelsize': self.font_size,
            'xtick.labelsize': self.font_size - 2,
            'ytick.labelsize': self.font_size - 2,
            'legend.fontsize': self.font_size - 2
        })
        
        return fig, axes


class AnalysisModule(ABC):
    """Abstract base class for all analysis modules."""
    
    def __init__(self, data_manager: DataManager, plot_manager: PlotManager):
        self.data_manager = data_manager
        self.plot_manager = plot_manager
        self.config = data_manager.config
        
    @abstractmethod
    def run_analysis(self) -> Dict[str, Any]:
        """Execute the analysis and return results."""
        pass
    
    @abstractmethod
    def get_module_name(self) -> str:
        """Return the module name."""
        pass
    
    def print_header(self):
        """Print module header."""
        name = self.get_module_name()
        print(f"\n{name}")
        print("=" * len(name))


class AnalysisResults:
    """Container for analysis results with summary capabilities."""
    
    def __init__(self):
        self.results = {}
        
    def add_module_result(self, module_name: str, result: Dict[str, Any]):
        """Add results from a module."""
        self.results[module_name] = result
        
    def get_summary(self) -> Dict[str, Any]:
        """Generate summary of all results."""
        summary = {
            'modules_completed': list(self.results.keys()),
            'total_modules': len(self.results),
            'timestamp': datetime.now().isoformat()
        }
        return summary
    
    def print_summary(self):
        """Print analysis summary."""
        print("\n" + "=" * 60)
        print("ANALYSIS SUMMARY")
        print("=" * 60)
        for module_name in self.results.keys():
            print(f"✓ {module_name}")
        print("=" * 60)
