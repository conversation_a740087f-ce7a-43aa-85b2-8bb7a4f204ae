#!/usr/bin/env python3
"""
Michaelis-Menten Kinetic Modeling Module
========================================

This module handles <PERSON><PERSON><PERSON><PERSON><PERSON> equation fitting for enzyme kinetics
analysis, estimating Vmax and Km parameters with confidence intervals.
"""

from typing import Dict, Any, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import curve_fit
from scipy import stats

from .base import AnalysisModule


class KineticModelingModule(AnalysisModule):
    """Module for Michaelis-Menten kinetic parameter estimation."""
    
    def get_module_name(self) -> str:
        return "Step 5: Michaelis-Menten Kinetic Modeling"
    
    def run_analysis(self) -> Dict[str, Any]:
        """Execute Michaelis-Menten fitting and visualization."""
        self.print_header()
        
        df = self.data_manager.load_data()
        
        # Prepare data for fitting
        fit_data = self._prepare_fitting_data(df)
        
        # Perform kinetic parameter estimation
        kinetic_params = self._estimate_kinetic_parameters(fit_data)
        
        # Create visualization as required
        self._create_kinetic_fits_visualization(fit_data, kinetic_params)
        
        return kinetic_params
    
    def _prepare_fitting_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for Michaelis-Menten fitting."""
        # Calculate means for each sample-concentration combination
        fit_data = df.groupby(['sample', 'concentration_value']).agg({
            'TMB': ['mean', 'std', 'count'],
            'OPD': ['mean', 'std', 'count']
        }).reset_index()
        
        # Flatten column names
        fit_data.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                           for col in fit_data.columns]
        
        return fit_data
    
    def michaelis_menten_equation(self, S: np.ndarray, Vmax: float, Km: float) -> np.ndarray:
        """Michaelis-Menten equation: V = (Vmax * S) / (Km + S)."""
        return (Vmax * S) / (Km + S)
    
    def _estimate_kinetic_parameters(self, fit_data: pd.DataFrame) -> Dict[str, Any]:
        """Estimate Vmax and Km parameters for each sample-substrate combination."""
        kinetic_params = {}
        
        print("Estimating Michaelis-Menten Parameters...")
        print("=" * 50)
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = fit_data[fit_data['sample'] == sample]
            kinetic_params[sample] = {}
            
            print(f"\nSample {sample}:")
            
            for substrate in ['TMB', 'OPD']:
                concentrations = sample_data['concentration_value'].values
                activities = sample_data[f'{substrate}_mean'].values
                errors = sample_data[f'{substrate}_std'].values
                
                try:
                    # Get module-specific configuration
                    config = self.config.get_module_config('kinetic_modeling')
                    max_iter = config.get('max_iterations', 5000)
                    optimization_method = config.get('optimization_method', 'lm')
                    
                    # Initial parameter estimates
                    if config.get('initial_guess_method', 'auto') == 'auto':
                        Vmax_init = max(activities) * 1.2  # Slightly higher than max observed
                        Km_init = concentrations[len(concentrations)//2]  # Middle concentration
                    else:
                        Vmax_init = max(activities)
                        Km_init = concentrations[0]
                    
                    # Fit Michaelis-Menten equation
                    popt, pcov = curve_fit(
                        self.michaelis_menten_equation,
                        concentrations, 
                        activities,
                        p0=[Vmax_init, Km_init],
                        maxfev=max_iter,
                        method=optimization_method,
                        sigma=errors if np.all(errors > 0) else None
                    )
                    
                    Vmax, Km = popt
                    
                    # Calculate parameter errors and confidence intervals
                    param_errors = np.sqrt(np.diag(pcov))
                    confidence_level = config.get('confidence_interval', 0.95)
                    alpha = 1 - confidence_level
                    t_val = stats.t.ppf(1 - alpha/2, len(concentrations) - 2)
                    
                    ci_95 = t_val * param_errors
                    
                    # Calculate goodness of fit
                    y_pred = self.michaelis_menten_equation(concentrations, Vmax, Km)
                    ss_res = np.sum((activities - y_pred) ** 2)
                    ss_tot = np.sum((activities - np.mean(activities)) ** 2)
                    r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
                    
                    # Calculate AIC and BIC
                    n = len(concentrations)
                    mse = ss_res / n
                    aic = n * np.log(mse) + 2 * 2  # 2 parameters
                    bic = n * np.log(mse) + np.log(n) * 2
                    
                    # Store parameters
                    param_dict = {
                        'Vmax': Vmax,
                        'Km': Km,
                        'Vmax_error': param_errors[0],
                        'Km_error': param_errors[1],
                        'Vmax_ci_lower': Vmax - ci_95[0],
                        'Vmax_ci_upper': Vmax + ci_95[0],
                        'Km_ci_lower': Km - ci_95[1],
                        'Km_ci_upper': Km + ci_95[1],
                        'r_squared': r_squared,
                        'aic': aic,
                        'bic': bic,
                        'convergence': True
                    }
                    
                    kinetic_params[sample][substrate] = param_dict
                    
                    # Store in data manager
                    self.data_manager.add_result(
                        analysis_step='michaelis_menten_params',
                        sample=sample,
                        substrate=substrate,
                        **param_dict
                    )
                    
                    print(f"  {substrate}: Vmax = {Vmax:.4f} ± {param_errors[0]:.4f}, "
                          f"Km = {Km:.2f} ± {param_errors[1]:.2f}, R² = {r_squared:.4f}")
                    
                except Exception as e:
                    print(f"  {substrate}: Fitting failed - {e}")
                    
                    # Store failure information
                    param_dict = {
                        'Vmax': np.nan,
                        'Km': np.nan,
                        'Vmax_error': np.nan,
                        'Km_error': np.nan,
                        'Vmax_ci_lower': np.nan,
                        'Vmax_ci_upper': np.nan,
                        'Km_ci_lower': np.nan,
                        'Km_ci_upper': np.nan,
                        'r_squared': np.nan,
                        'aic': np.nan,
                        'bic': np.nan,
                        'convergence': False,
                        'error': str(e)
                    }
                    
                    kinetic_params[sample][substrate] = param_dict
                    
                    self.data_manager.add_result(
                        analysis_step='michaelis_menten_params',
                        sample=sample,
                        substrate=substrate,
                        **param_dict
                    )
        
        return kinetic_params
    
    def _create_kinetic_fits_visualization(self, fit_data: pd.DataFrame, kinetic_params: Dict[str, Any]):
        """Create Michaelis-Menten fits visualization as specifically required."""
        
        # Create the main 12-subplot figure as required
        self._create_required_fits_plot(fit_data, kinetic_params)
        
        # Create individual plots for each sample-substrate combination
        self._create_individual_fits_plots(fit_data, kinetic_params)
    
    def _create_required_fits_plot(self, fit_data: pd.DataFrame, kinetic_params: Dict[str, Any]):
        """Create 12-subplot Michaelis-Menten fits as specifically required."""
        def plot_mm_fits(ax):
            fig, axes = plt.subplots(2, 6, figsize=(24, 12))
            
            samples = ['A', 'B', 'C', 'D', 'E', 'F']
            substrates = ['TMB', 'OPD']
            
            plot_idx = 0
            fit_results = []
            
            for sub_idx, substrate in enumerate(substrates):
                for sam_idx, sample in enumerate(samples):
                    ax_current = axes[sub_idx, sam_idx]
                    
                    sample_data = fit_data[fit_data['sample'] == sample]
                    concentrations = sample_data['concentration_value'].values
                    activities = sample_data[f'{substrate}_mean'].values
                    errors = sample_data[f'{substrate}_std'].values
                    
                    # Plot raw data points
                    ax_current.errorbar(concentrations, activities, yerr=errors, 
                                      fmt='o', color='blue', capsize=5, capthick=2,
                                      markersize=8, alpha=0.7, label='Data')
                    
                    # Plot fitted curve if successful
                    params = kinetic_params.get(sample, {}).get(substrate, {})
                    if params.get('convergence', False):
                        Vmax = params['Vmax']
                        Km = params['Km']
                        r_squared = params['r_squared']
                        
                        # Generate smooth curve
                        S_range = np.linspace(min(concentrations), max(concentrations), 100)
                        fitted_curve = self.michaelis_menten_equation(S_range, Vmax, Km)
                        
                        ax_current.plot(S_range, fitted_curve, 'r-', linewidth=3, alpha=0.8,
                                      label=f'M-M Fit (R²={r_squared:.3f})')
                        
                        # Add parameter text
                        param_text = f'Vmax = {Vmax:.3f}\nKm = {Km:.1f} μM'
                        ax_current.text(0.05, 0.95, param_text, transform=ax_current.transAxes,
                                      fontsize=10, verticalalignment='top',
                                      bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                        
                        # Store fit results for export
                        for s_val, fitted_val in zip(S_range, fitted_curve):
                            fit_results.append({
                                'sample': sample,
                                'substrate': substrate,
                                'concentration': s_val,
                                'fitted_value': fitted_val,
                                'Vmax': Vmax,
                                'Km': Km,
                                'r_squared': r_squared
                            })
                    else:
                        ax_current.text(0.5, 0.5, 'Fit Failed', transform=ax_current.transAxes,
                                      ha='center', va='center', fontsize=12, color='red')
                    
                    ax_current.set_xlabel('Concentration (μM)', fontsize=10)
                    ax_current.set_ylabel(f'{substrate} Activity', fontsize=10)
                    ax_current.set_title(f'Sample {sample} - {substrate}', fontsize=12, fontweight='bold')
                    ax_current.legend(fontsize=8)
                    ax_current.grid(True, alpha=0.3)
                    ax_current.tick_params(labelsize=9)
            
            plt.suptitle('Michaelis-Menten Kinetic Parameter Fits', fontsize=18, fontweight='bold')
            plt.tight_layout()
            return pd.DataFrame(fit_results)
        
        # This is a special case where we create the plot directly due to complex subplot structure
        fig = plt.figure(figsize=(24, 12))
        
        samples = ['A', 'B', 'C', 'D', 'E', 'F']
        substrates = ['TMB', 'OPD']
        fit_results = []
        
        for sub_idx, substrate in enumerate(substrates):
            for sam_idx, sample in enumerate(samples):
                ax = plt.subplot(2, 6, sub_idx * 6 + sam_idx + 1)
                
                sample_data = fit_data[fit_data['sample'] == sample]
                concentrations = sample_data['concentration_value'].values
                activities = sample_data[f'{substrate}_mean'].values
                errors = sample_data[f'{substrate}_std'].values
                
                # Plot raw data points
                ax.errorbar(concentrations, activities, yerr=errors, 
                          fmt='o', color='blue', capsize=5, capthick=2,
                          markersize=8, alpha=0.7, label='Data')
                
                # Plot fitted curve if successful
                params = kinetic_params.get(sample, {}).get(substrate, {})
                if params.get('convergence', False):
                    Vmax = params['Vmax']
                    Km = params['Km']
                    r_squared = params['r_squared']
                    
                    # Generate smooth curve
                    S_range = np.linspace(min(concentrations), max(concentrations), 100)
                    fitted_curve = self.michaelis_menten_equation(S_range, Vmax, Km)
                    
                    ax.plot(S_range, fitted_curve, 'r-', linewidth=3, alpha=0.8,
                           label=f'M-M Fit (R²={r_squared:.3f})')
                    
                    # Add parameter text
                    param_text = f'Vmax = {Vmax:.3f}\nKm = {Km:.1f} μM'
                    ax.text(0.05, 0.95, param_text, transform=ax.transAxes,
                           fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                    
                    # Store fit results for export
                    for s_val, fitted_val in zip(S_range, fitted_curve):
                        fit_results.append({
                            'sample': sample,
                            'substrate': substrate,
                            'concentration': s_val,
                            'fitted_value': fitted_val,
                            'Vmax': Vmax,
                            'Km': Km,
                            'r_squared': r_squared
                        })
                else:
                    ax.text(0.5, 0.5, 'Fit Failed', transform=ax.transAxes,
                           ha='center', va='center', fontsize=12, color='red')
                
                ax.set_xlabel('Concentration (μM)', fontsize=10)
                ax.set_ylabel(f'{substrate} Activity', fontsize=10)
                ax.set_title(f'Sample {sample} - {substrate}', fontsize=12, fontweight='bold')
                ax.legend(fontsize=8)
                ax.grid(True, alpha=0.3)
                ax.tick_params(labelsize=9)
        
        plt.suptitle('Michaelis-Menten Kinetic Parameter Fits', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        # Save the plot with data
        fit_results_df = pd.DataFrame(fit_results)
        self.plot_manager.save_plot('michaelis_menten_fits.png', data=fit_results_df)
    
    def _create_individual_fits_plots(self, fit_data: pd.DataFrame, kinetic_params: Dict[str, Any]):
        """Create individual Michaelis-Menten fit plots for each combination."""
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            for substrate in ['TMB', 'OPD']:
                def plot_individual_fit(ax, sample=sample, substrate=substrate):
                    sample_data = fit_data[fit_data['sample'] == sample]
                    concentrations = sample_data['concentration_value'].values
                    activities = sample_data[f'{substrate}_mean'].values
                    errors = sample_data[f'{substrate}_std'].values
                    
                    # Plot raw data
                    ax.errorbar(concentrations, activities, yerr=errors, 
                              fmt='o', color='blue', capsize=5, capthick=2,
                              markersize=10, alpha=0.7, label='Experimental Data')
                    
                    # Plot fitted curve
                    params = kinetic_params.get(sample, {}).get(substrate, {})
                    if params.get('convergence', False):
                        Vmax = params['Vmax']
                        Km = params['Km']
                        r_squared = params['r_squared']
                        
                        S_range = np.linspace(min(concentrations), max(concentrations)*1.2, 100)
                        fitted_curve = self.michaelis_menten_equation(S_range, Vmax, Km)
                        
                        ax.plot(S_range, fitted_curve, 'r-', linewidth=3, alpha=0.8,
                               label='Michaelis-Menten Fit')
                        
                        # Add statistics
                        stats_text = (f'Vmax = {Vmax:.4f} ± {params["Vmax_error"]:.4f}\n'
                                     f'Km = {Km:.2f} ± {params["Km_error"]:.2f} μM\n'
                                     f'R² = {r_squared:.4f}')
                        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
                               fontsize=12, verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
                    
                    ax.set_xlabel('Concentration (μM)', fontsize=14)
                    ax.set_ylabel(f'{substrate} Activity', fontsize=14)
                    ax.set_title(f'Michaelis-Menten Fit: Sample {sample} - {substrate}', 
                                fontsize=16, fontweight='bold')
                    ax.legend(fontsize=12)
                    ax.grid(True, alpha=0.3)
                    ax.tick_params(labelsize=12)
                
                # Prepare individual data for export
                individual_data = fit_data[fit_data['sample'] == sample][
                    ['concentration_value', f'{substrate}_mean', f'{substrate}_std']
                ].copy()
                individual_data.columns = ['concentration', 'mean_activity', 'std_activity']
                individual_data['sample'] = sample
                individual_data['substrate'] = substrate
                
                # Add fitted curve data if available
                params = kinetic_params.get(sample, {}).get(substrate, {})
                if params.get('convergence', False):
                    individual_data['Vmax'] = params['Vmax']
                    individual_data['Km'] = params['Km']
                    individual_data['r_squared'] = params['r_squared']
                
                filename = f'mm_fit_{sample}_{substrate}.png'
                self.plot_manager.save_single_plot(filename, plot_individual_fit, 
                                                 data=individual_data)
