# Data Analysis Protocol: Enzyme Kinetics and Machine Learning

## Role and Objective

You are a Python data scientist tasked with a comprehensive analysis of the provided biological experiment data (`sample0513.csv`). Your output must be strictly structured, including specific visualizations and a consolidated numerical results file.

## Environment & Input

* **Python Environment:** 3.12.7
* **Input Data File:** `sample0513.csv`
* **Key Dependencies:** `pandas`, `numpy`, `scipy`, `matplotlib`, `seaborn`, `scikit-learn`, `shap`, `statsmodels`.

## Output Specifications

1. **Visualizations:** Save all generated plots as high-quality PNG files. File names must be descriptive (e.g., `kinetic_curve_TMB.png`, `shap_summary_plot.png`).
2. **Numerical Data:** Create a single CSV file named `analysis_results.csv`. This file must contain all key numerical outputs from every analysis step (e.g., parameters, CVs, P-values, scores). Include a column named `analysis_step` to identify the origin of the data.

## Analysis Steps

### Step 1: Data Loading and Preparation

- **Action:** Load `sample0513.csv` into a pandas DataFrame. Convert `concentration` from string (e.g., "5 μM") to float. Calculate descriptive statistics for `TMB` and `OPD`.
- **Visualization:** Generate box plots comparing `TMB` and `OPD` distributions across all `sample` types (A-F).
- **CSV Output:** Save the descriptive statistics to `analysis_results.csv` (`analysis_step: descriptive_stats`).

### Step 2: Kinetic Curve Visualization

- **Action:** Group data by `sample` and `concentration`. Calculate the mean and 95% confidence interval (CI) for `TMB` and `OPD`.
- **Visualization:** Create two separate line plots (TMB and OPD) with `concentration` on the log-scale x-axis, `sample` as hue, showing the mean and CI.
- **CSV Output:** Save the mean and CI data to `analysis_results.csv` (`analysis_step: kinetic_curve_data`).

### Step 3: Quality Control (QC) and Correlation

- **Action:** Calculate the Coefficient of Variation (CV%) for parallel replicates (A1, A2, A3 etc.) for each `sample`-`concentration` group in both `TMB` and `OPD`. Calculate the overall Pearson correlation between TMB and OPD readings.
- **Visualization:**
  - Generate a scatter plot showing TMB vs. OPD correlation, with a regression line and R² value.
  - Create a heatmap of CV% values, highlighting groups with CV > 15%.
- **CSV Output:** Save CV% and ICC (if calculated) values to `analysis_results.csv` (`analysis_step: quality_control`).

### Step 4: Multivariate Analysis of Variance (MANOVA)

- **Action:** Perform a MANOVA using `statsmodels` to test the main effects of `sample` and `concentration`, and their interaction, on the combined `(TMB, OPD)` response variables.
- **Visualization:** Generate interaction plots showing the means of `TMB` and `OPD` as a function of `concentration` for each `sample`.
- **CSV Output:** Save the MANOVA results (e.g., Pillai's trace, F-statistics, p-values) to `analysis_results.csv` (`analysis_step: manova_results`).

### Step 5: Michaelis-Menten Kinetic Parameter Estimation

- **Action:** For each `sample` (A-F) and `substrate` (TMB, OPD), fit the Michaelis-Menten equation `V = (Vmax * S) / (Km + S)` using `scipy.optimize.curve_fit`. Calculate 95% confidence intervals for Vmax and Km.
- **Visualization:** Create 12 subplots (6 samples x 2 substrates), displaying the fitted curve over the raw data points.
- **CSV Output:** Save Vmax, Km, and their 95% CI bounds to `analysis_results.csv` (`analysis_step: michaelis_menten_params`).

### Step 6: Machine Learning Classification

- **Action:**
  - **Feature Engineering:** Create new features (e.g., `TMB/OPD ratio`, `log(concentration)`).
  - **Model Training:** Train a Gradient Boosting Classifier and a simple 1D-CNN (if applicable) to classify `sample` (A-F) based on the features.
  - **Evaluation:** Perform cross-validation and generate classification reports.
- **Visualization:** Generate confusion matrices (normalized) for the best-performing model.
- **CSV Output:** Save the classification report metrics (precision, recall, F1-score, accuracy) to `analysis_results.csv` (`analysis_step: ml_classification_report`).

### Step 7: Model Explainability (SHAP & t-SNE)

- **Action:** Use the `shap` library to calculate SHAP values for the Gradient Boosting model. Apply t-SNE for dimensionality reduction on the feature set.
- **Visualization:**
  - Generate a SHAP summary plot showing global feature importance.
  - Generate a t-SNE plot to visualize clustering of samples in a 2D space, colored by `sample`.
- **CSV Output:** Save the average absolute SHAP values for each feature to `analysis_results.csv` (`analysis_step: shap_feature_importance`).

### Step 8: Hierarchical Clustering

- **Action:** Based on the derived parameters (Vmax, Km) and key features identified by SHAP, perform hierarchical clustering of the samples (A-F).
- **Visualization:** Generate a dendrogram and a corresponding heatmap of the normalized key features, ordered by the clustering result.
- **CSV Output:** Save the clustering results (which cluster each sample belongs to) to `analysis_results.csv` (`analysis_step: clustering_results`).

### Step 9: Anomaly Detection

- **Action:** Use the Isolation Forest algorithm on the combined `TMB` and `OPD` data to identify outliers (potential anomalies).
- **Visualization:** Create a scatter plot (e.g., `concentration` vs. `TMB`) where points are colored or sized by their anomaly score.
- **CSV Output:** Save the anomaly scores and classification (outlier/inlier) for each data point to `analysis_results.csv` (`analysis_step: anomaly_detection_scores`).

**End of Protocol**
