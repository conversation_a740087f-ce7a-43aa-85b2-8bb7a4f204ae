#!/usr/bin/env python3
"""
Comprehensive Enzyme Kinetics Data Analysis
===========================================

This script performs a complete analysis of enzyme kinetics data following
a 9-step protocol including statistical analysis, kinetic modeling, machine
learning, and visualization.

Author: Data Scientist
Date: 2025-01-15
Environment: Python 3.12.7
"""

import os
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Any

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.optimize import curve_fit
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import cross_validate, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.manifold import TSNE
from sklearn.cluster import AgglomerativeClustering
import shap
import statsmodels.api as sm
from statsmodels.multivariate.manova import MANOVA
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set style for better visualization [[memory:4927745]]
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12

class EnzymeKineticsAnalyzer:
    """
    Comprehensive analyzer for enzyme kinetics data with statistical modeling,
    machine learning, and visualization capabilities.
    """
    
    def __init__(self, data_file: str):
        """Initialize analyzer with data file path."""
        self.data_file = data_file
        self.df = None
        self.results = []
        self.timestamp = datetime.now().strftime("%Y%m%d%H%M")
        self.output_dir = f"/Users/<USER>/Documents/nie/outputs/{self.timestamp}"
        self.create_output_directories()
        
    def create_output_directories(self):
        """Create timestamped output directories."""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(f"{self.output_dir}/visualizations", exist_ok=True)
        os.makedirs(f"{self.output_dir}/models", exist_ok=True)
        os.makedirs(f"{self.output_dir}/logs", exist_ok=True)
        os.makedirs(f"{self.output_dir}/metadata", exist_ok=True)
        
    def save_results_to_csv(self):
        """Save all numerical results to consolidated CSV file."""
        if self.results:
            results_df = pd.DataFrame(self.results)
            results_file = "/Users/<USER>/Documents/nie/analysis_results.csv"
            results_df.to_csv(results_file, index=False)
            print(f"Results saved to: {results_file}")
            
    def save_plot(self, filename: str):
        """Save current plot with standardized naming."""
        filepath = f"{self.output_dir}/visualizations/{filename}"
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.show()
        
    def step1_data_loading_preparation(self):
        """Step 1: Data loading and preparation with descriptive statistics."""
        print("Step 1: Data Loading and Preparation")
        print("=" * 50)
        
        # Load data
        self.df = pd.read_csv(self.data_file)
        
        # Convert concentration from string to float
        self.df['concentration_value'] = self.df['concentration'].str.extract(r'(\d+)').astype(float)
        
        # Calculate descriptive statistics
        desc_stats = self.df[['TMB', 'OPD']].describe()
        print("Descriptive Statistics:")
        print(desc_stats)
        
        # Store results
        for substrate in ['TMB', 'OPD']:
            for stat in desc_stats.index:
                self.results.append({
                    'analysis_step': 'descriptive_stats',
                    'substrate': substrate,
                    'statistic': stat,
                    'value': desc_stats.loc[stat, substrate]
                })
        
        # Generate box plots
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        sns.boxplot(data=self.df, x='sample', y='TMB')
        plt.title('TMB Distribution by Sample')
        plt.ylabel('TMB Activity')
        
        plt.subplot(1, 2, 2)
        sns.boxplot(data=self.df, x='sample', y='OPD')
        plt.title('OPD Distribution by Sample')
        plt.ylabel('OPD Activity')
        
        plt.tight_layout()
        self.save_plot('substrate_distributions_boxplot.png')
        
    def step2_kinetic_curve_visualization(self):
        """Step 2: Kinetic curve visualization with confidence intervals."""
        print("\nStep 2: Kinetic Curve Visualization")
        print("=" * 50)
        
        # Group by sample and concentration, calculate means and CIs
        grouped = self.df.groupby(['sample', 'concentration_value']).agg({
            'TMB': ['mean', 'std', 'count'],
            'OPD': ['mean', 'std', 'count']
        }).reset_index()
        
        # Flatten column names
        grouped.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0] 
                          for col in grouped.columns]
        
        # Calculate 95% CI
        confidence_level = 0.95
        for substrate in ['TMB', 'OPD']:
            grouped[f'{substrate}_ci'] = grouped[f'{substrate}_std'] / np.sqrt(grouped[f'{substrate}_count']) * \
                                       stats.t.ppf((1 + confidence_level) / 2, grouped[f'{substrate}_count'] - 1)
        
        # Store kinetic curve data
        for _, row in grouped.iterrows():
            for substrate in ['TMB', 'OPD']:
                self.results.append({
                    'analysis_step': 'kinetic_curve_data',
                    'sample': row['sample'],
                    'concentration': row['concentration_value'],
                    'substrate': substrate,
                    'mean': row[f'{substrate}_mean'],
                    'std': row[f'{substrate}_std'],
                    'ci_95': row[f'{substrate}_ci']
                })
        
        # Create kinetic curve plots
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        for i, substrate in enumerate(['TMB', 'OPD']):
            for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
                sample_data = grouped[grouped['sample'] == sample]
                axes[i].errorbar(sample_data['concentration_value'], 
                               sample_data[f'{substrate}_mean'],
                               yerr=sample_data[f'{substrate}_ci'],
                               label=f'Sample {sample}', marker='o', capsize=5)
            
            axes[i].set_xscale('log')
            axes[i].set_xlabel('Concentration (μM)')
            axes[i].set_ylabel(f'{substrate} Activity')
            axes[i].set_title(f'{substrate} Kinetic Curves')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.save_plot('kinetic_curves_with_ci.png')
        
    def step3_quality_control_correlation(self):
        """Step 3: Quality control analysis and correlation."""
        print("\nStep 3: Quality Control and Correlation Analysis")
        print("=" * 50)
        
        # Calculate CV% for parallel replicates
        cv_results = []
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            for conc in self.df['concentration_value'].unique():
                subset = self.df[(self.df['sample'] == sample) & 
                               (self.df['concentration_value'] == conc)]
                
                for substrate in ['TMB', 'OPD']:
                    values = subset[substrate]
                    if len(values) > 1:
                        cv = (values.std() / values.mean()) * 100
                        cv_results.append({
                            'sample': sample,
                            'concentration': conc,
                            'substrate': substrate,
                            'cv_percent': cv
                        })
                        
                        self.results.append({
                            'analysis_step': 'quality_control',
                            'sample': sample,
                            'concentration': conc,
                            'substrate': substrate,
                            'cv_percent': cv
                        })
        
        cv_df = pd.DataFrame(cv_results)
        
        # Calculate overall correlation between TMB and OPD
        correlation = stats.pearsonr(self.df['TMB'], self.df['OPD'])
        print(f"TMB-OPD Correlation: r = {correlation[0]:.4f}, p = {correlation[1]:.4f}")
        
        self.results.append({
            'analysis_step': 'quality_control',
            'metric': 'pearson_correlation',
            'value': correlation[0],
            'p_value': correlation[1]
        })
        
        # Visualization
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))
        
        # Correlation scatter plot
        axes[0].scatter(self.df['TMB'], self.df['OPD'], alpha=0.7)
        z = np.polyfit(self.df['TMB'], self.df['OPD'], 1)
        p = np.poly1d(z)
        axes[0].plot(self.df['TMB'], p(self.df['TMB']), "r--", alpha=0.8)
        axes[0].set_xlabel('TMB Activity')
        axes[0].set_ylabel('OPD Activity')
        axes[0].set_title(f'TMB vs OPD Correlation\nR² = {correlation[0]**2:.4f}')
        
        # CV% heatmaps
        for i, substrate in enumerate(['TMB', 'OPD']):
            cv_pivot = cv_df[cv_df['substrate'] == substrate].pivot(
                index='sample', columns='concentration', values='cv_percent')
            
            im = sns.heatmap(cv_pivot, annot=True, fmt='.1f', cmap='RdYlBu_r',
                           ax=axes[i+1], cbar_kws={'label': 'CV%'})
            axes[i+1].set_title(f'{substrate} CV% Heatmap')
            
            # Highlight CV > 15%
            for (i_idx, j_idx), value in np.ndenumerate(cv_pivot.values):
                if not np.isnan(value) and value > 15:
                    axes[i+1].add_patch(plt.Rectangle((j_idx, i_idx), 1, 1,
                                                    fill=False, edgecolor='red',
                                                    lw=3))
        
        plt.tight_layout()
        self.save_plot('quality_control_analysis.png')
        
    def step4_manova_analysis(self):
        """Step 4: Multivariate Analysis of Variance (MANOVA)."""
        print("\nStep 4: MANOVA Analysis")
        print("=" * 50)
        
        # Prepare data for MANOVA
        manova_data = self.df[['sample', 'concentration_value', 'TMB', 'OPD']].copy()
        
        # Perform MANOVA
        try:
            manova = MANOVA.from_formula('TMB + OPD ~ sample + concentration_value + sample:concentration_value',
                                       data=manova_data)
            manova_results = manova.mv_test()
            print("MANOVA Results:")
            print(manova_results)
            
            # Store MANOVA results
            for effect in manova_results.results.keys():
                stats_dict = manova_results.results[effect]
                if "Pillai's trace" in stats_dict:
                    pillai_stats = stats_dict["Pillai's trace"]
                    self.results.append({
                        'analysis_step': 'manova_results',
                        'effect': effect,
                        'test_statistic': 'Pillai_trace',
                        'value': pillai_stats['Value'],
                        'f_statistic': pillai_stats['F Value'],
                        'p_value': pillai_stats['Pr > F']
                    })
        
        except Exception as e:
            print(f"MANOVA analysis failed: {e}")
        
        # Interaction plots
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        for i, substrate in enumerate(['TMB', 'OPD']):
            # Calculate means for interaction plot
            interaction_data = self.df.groupby(['sample', 'concentration_value'])[substrate].mean().reset_index()
            
            for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
                sample_data = interaction_data[interaction_data['sample'] == sample]
                axes[i].plot(sample_data['concentration_value'], sample_data[substrate],
                           marker='o', label=f'Sample {sample}')
            
            axes[i].set_xscale('log')
            axes[i].set_xlabel('Concentration (μM)')
            axes[i].set_ylabel(f'{substrate} Activity')
            axes[i].set_title(f'{substrate} Interaction Plot')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.save_plot('manova_interaction_plots.png')
        
    def michaelis_menten_equation(self, S: np.ndarray, Vmax: float, Km: float) -> np.ndarray:
        """Michaelis-Menten equation: V = (Vmax * S) / (Km + S)."""
        return (Vmax * S) / (Km + S)
        
    def step5_michaelis_menten_fitting(self):
        """Step 5: Michaelis-Menten kinetic parameter estimation."""
        print("\nStep 5: Michaelis-Menten Parameter Estimation")
        print("=" * 50)
        
        # Prepare data for fitting
        fit_data = self.df.groupby(['sample', 'concentration_value']).agg({
            'TMB': 'mean',
            'OPD': 'mean'
        }).reset_index()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        plot_idx = 0
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = fit_data[fit_data['sample'] == sample]
            concentrations = sample_data['concentration_value'].values
            
            ax = axes[plot_idx]
            
            for substrate in ['TMB', 'OPD']:
                activities = sample_data[substrate].values
                
                try:
                    # Initial parameter estimates
                    Vmax_init = max(activities)
                    Km_init = concentrations[len(concentrations)//2]
                    
                    # Fit Michaelis-Menten equation
                    popt, pcov = curve_fit(self.michaelis_menten_equation,
                                         concentrations, activities,
                                         p0=[Vmax_init, Km_init],
                                         maxfev=5000)
                    
                    Vmax, Km = popt
                    
                    # Calculate 95% confidence intervals
                    param_errors = np.sqrt(np.diag(pcov))
                    ci_95 = 1.96 * param_errors
                    
                    # Store parameters
                    self.results.append({
                        'analysis_step': 'michaelis_menten_params',
                        'sample': sample,
                        'substrate': substrate,
                        'Vmax': Vmax,
                        'Km': Km,
                        'Vmax_ci_lower': Vmax - ci_95[0],
                        'Vmax_ci_upper': Vmax + ci_95[0],
                        'Km_ci_lower': Km - ci_95[1],
                        'Km_ci_upper': Km + ci_95[1]
                    })
                    
                    # Plot fitted curve
                    S_range = np.linspace(min(concentrations), max(concentrations), 100)
                    fitted_curve = self.michaelis_menten_equation(S_range, Vmax, Km)
                    
                    ax.scatter(concentrations, activities, label=f'{substrate} data', alpha=0.7)
                    ax.plot(S_range, fitted_curve, '--', 
                           label=f'{substrate} fit (Vmax={Vmax:.3f}, Km={Km:.1f})')
                    
                except Exception as e:
                    print(f"Fitting failed for Sample {sample}, {substrate}: {e}")
            
            ax.set_xlabel('Concentration (μM)')
            ax.set_ylabel('Activity')
            ax.set_title(f'Sample {sample} M-M Kinetics')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plot_idx += 1
        
        plt.tight_layout()
        self.save_plot('michaelis_menten_fits.png')
        
    def step6_machine_learning_classification(self):
        """Step 6: Machine learning classification."""
        print("\nStep 6: Machine Learning Classification")
        print("=" * 50)
        
        # Feature engineering
        ml_data = self.df.copy()
        ml_data['TMB_OPD_ratio'] = ml_data['TMB'] / ml_data['OPD']
        ml_data['log_concentration'] = np.log10(ml_data['concentration_value'])
        ml_data['TMB_squared'] = ml_data['TMB'] ** 2
        ml_data['OPD_squared'] = ml_data['OPD'] ** 2
        
        # Prepare features and target
        feature_columns = ['TMB', 'OPD', 'concentration_value', 'TMB_OPD_ratio', 
                          'log_concentration', 'TMB_squared', 'OPD_squared']
        X = ml_data[feature_columns]
        y = ml_data['sample']
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train Random Forest Classifier (better supported by SHAP)
        rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        
        # Cross-validation
        cv_scores = cross_validate(rf_classifier, X_scaled, y, 
                                 cv=StratifiedKFold(n_splits=3, shuffle=True, random_state=42),
                                 scoring=['accuracy', 'precision_macro', 'recall_macro', 'f1_macro'],
                                 return_train_score=True)
        
        # Fit model for further analysis
        rf_classifier.fit(X_scaled, y)
        y_pred = rf_classifier.predict(X_scaled)
        
        # Classification report
        class_report = classification_report(y, y_pred, output_dict=True)
        
        # Store classification results
        for metric in ['accuracy', 'macro avg', 'weighted avg']:
            if metric in class_report:
                if isinstance(class_report[metric], dict):
                    self.results.append({
                        'analysis_step': 'ml_classification_report',
                        'metric': metric,
                        'precision': class_report[metric].get('precision', np.nan),
                        'recall': class_report[metric].get('recall', np.nan),
                        'f1_score': class_report[metric].get('f1-score', np.nan),
                        'support': class_report[metric].get('support', np.nan)
                    })
                else:  # For accuracy which is just a float
                    self.results.append({
                        'analysis_step': 'ml_classification_report',
                        'metric': metric,
                        'value': class_report[metric]
                    })
        
        # Confusion matrix visualization
        cm = confusion_matrix(y, y_pred)
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                   xticklabels=['A', 'B', 'C', 'D', 'E', 'F'],
                   yticklabels=['A', 'B', 'C', 'D', 'E', 'F'])
        plt.title('Normalized Confusion Matrix - Random Forest')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        self.save_plot('confusion_matrix_random_forest.png')
        
        # Store model for SHAP analysis
        self.rf_model = rf_classifier
        self.feature_names = feature_columns
        self.X_scaled = X_scaled
        
    def step7_model_explainability(self):
        """Step 7: Model explainability with SHAP and t-SNE."""
        print("\nStep 7: Model Explainability Analysis")
        print("=" * 50)
        
        # SHAP analysis
        explainer = shap.TreeExplainer(self.rf_model)
        shap_values = explainer.shap_values(self.X_scaled)
        
        # Calculate average absolute SHAP values for feature importance
        if len(shap_values.shape) == 3:  # Multi-class
            mean_shap = np.abs(shap_values).mean(axis=(0, 2))
        else:
            mean_shap = np.abs(shap_values).mean(axis=0)
        
        # Store SHAP feature importance
        for i, feature in enumerate(self.feature_names):
            self.results.append({
                'analysis_step': 'shap_feature_importance',
                'feature': feature,
                'mean_absolute_shap': mean_shap[i]
            })
        
        # SHAP summary plot
        plt.figure(figsize=(10, 6))
        if len(shap_values.shape) == 3:
            shap.summary_plot(shap_values[:, :, 0], self.X_scaled, 
                            feature_names=self.feature_names, show=False)
        else:
            shap.summary_plot(shap_values, self.X_scaled, 
                            feature_names=self.feature_names, show=False)
        plt.title('SHAP Feature Importance Summary')
        self.save_plot('shap_summary_plot.png')
        plt.close()
        
        # t-SNE visualization
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(self.X_scaled)//4))
        X_tsne = tsne.fit_transform(self.X_scaled)
        
        plt.figure(figsize=(10, 8))
        samples = self.df['sample'].values
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            mask = samples == sample
            plt.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                       label=f'Sample {sample}', alpha=0.7, s=50)
        
        plt.xlabel('t-SNE Component 1')
        plt.ylabel('t-SNE Component 2')
        plt.title('t-SNE Visualization of Sample Clustering')
        plt.legend()
        plt.grid(True, alpha=0.3)
        self.save_plot('tsne_sample_clustering.png')
        
    def step8_hierarchical_clustering(self):
        """Step 8: Hierarchical clustering analysis."""
        print("\nStep 8: Hierarchical Clustering Analysis")
        print("=" * 50)
        
        # Create feature matrix for each sample using kinetic parameters and key features
        sample_features = []
        sample_names = []
        
        for sample in ['A', 'B', 'C', 'D', 'E', 'F']:
            sample_data = self.df[self.df['sample'] == sample]
            
            # Calculate sample-level features
            features = {
                'TMB_mean': sample_data['TMB'].mean(),
                'OPD_mean': sample_data['OPD'].mean(),
                'TMB_std': sample_data['TMB'].std(),
                'OPD_std': sample_data['OPD'].std(),
                'TMB_OPD_corr': sample_data['TMB'].corr(sample_data['OPD'])
            }
            
            sample_features.append(list(features.values()))
            sample_names.append(sample)
        
        # Convert to array and normalize
        feature_matrix = np.array(sample_features)
        feature_matrix = StandardScaler().fit_transform(feature_matrix)
        
        # Hierarchical clustering
        linkage_matrix = linkage(feature_matrix, method='ward')
        
        # Generate dendrogram
        plt.figure(figsize=(12, 8))
        dendrogram(linkage_matrix, labels=sample_names, leaf_rotation=0)
        plt.title('Hierarchical Clustering Dendrogram')
        plt.ylabel('Distance')
        plt.xlabel('Sample')
        self.save_plot('hierarchical_clustering_dendrogram.png')
        
        # Get cluster assignments (using 3 clusters)
        clustering = AgglomerativeClustering(n_clusters=3, linkage='ward')
        cluster_labels = clustering.fit_predict(feature_matrix)
        
        # Store clustering results
        for i, sample in enumerate(sample_names):
            self.results.append({
                'analysis_step': 'clustering_results',
                'sample': sample,
                'cluster': cluster_labels[i]
            })
        
        # Feature heatmap ordered by clustering
        feature_names = ['TMB_mean', 'OPD_mean', 'TMB_std', 'OPD_std', 'TMB_OPD_corr']
        
        plt.figure(figsize=(10, 6))
        sns.heatmap(feature_matrix, 
                   xticklabels=feature_names,
                   yticklabels=sample_names,
                   annot=True, fmt='.2f', cmap='RdBu_r', center=0)
        plt.title('Normalized Feature Heatmap (Ordered by Clustering)')
        self.save_plot('clustering_feature_heatmap.png')
        
    def step9_anomaly_detection(self):
        """Step 9: Anomaly detection using Isolation Forest."""
        print("\nStep 9: Anomaly Detection Analysis")
        print("=" * 50)
        
        # Prepare data for anomaly detection
        anomaly_features = self.df[['TMB', 'OPD', 'concentration_value']].copy()
        
        # Fit Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_scores = iso_forest.decision_function(anomaly_features)
        anomaly_labels = iso_forest.fit_predict(anomaly_features)
        
        # Store anomaly detection results
        for i, (score, label) in enumerate(zip(anomaly_scores, anomaly_labels)):
            self.results.append({
                'analysis_step': 'anomaly_detection_scores',
                'data_point': i,
                'anomaly_score': score,
                'is_outlier': label == -1,
                'sample': self.df.iloc[i]['sample'],
                'concentration': self.df.iloc[i]['concentration_value']
            })
        
        # Visualization
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Concentration vs TMB colored by anomaly score
        scatter = axes[0].scatter(self.df['concentration_value'], self.df['TMB'],
                                c=anomaly_scores, cmap='RdYlBu', alpha=0.7)
        axes[0].set_xlabel('Concentration (μM)')
        axes[0].set_ylabel('TMB Activity')
        axes[0].set_title('Anomaly Scores: Concentration vs TMB')
        axes[0].set_xscale('log')
        plt.colorbar(scatter, ax=axes[0], label='Anomaly Score')
        
        # Mark outliers
        outliers = anomaly_labels == -1
        axes[0].scatter(self.df.loc[outliers, 'concentration_value'],
                       self.df.loc[outliers, 'TMB'],
                       c='red', s=100, marker='x', label='Outliers')
        axes[0].legend()
        
        # TMB vs OPD colored by anomaly score
        scatter2 = axes[1].scatter(self.df['TMB'], self.df['OPD'],
                                 c=anomaly_scores, cmap='RdYlBu', alpha=0.7)
        axes[1].set_xlabel('TMB Activity')
        axes[1].set_ylabel('OPD Activity')
        axes[1].set_title('Anomaly Scores: TMB vs OPD')
        plt.colorbar(scatter2, ax=axes[1], label='Anomaly Score')
        
        # Mark outliers
        axes[1].scatter(self.df.loc[outliers, 'TMB'],
                       self.df.loc[outliers, 'OPD'],
                       c='red', s=100, marker='x', label='Outliers')
        axes[1].legend()
        
        plt.tight_layout()
        self.save_plot('anomaly_detection_visualization.png')
        
        print(f"Detected {sum(outliers)} outliers out of {len(self.df)} data points")
        
    def run_complete_analysis(self):
        """Execute all analysis steps in sequence."""
        print("ENZYME KINETICS COMPREHENSIVE ANALYSIS")
        print("=" * 60)
        print(f"Timestamp: {self.timestamp}")
        print(f"Output Directory: {self.output_dir}")
        print("=" * 60)
        
        # Execute all steps
        self.step1_data_loading_preparation()
        self.step2_kinetic_curve_visualization()
        self.step3_quality_control_correlation()
        self.step4_manova_analysis()
        self.step5_michaelis_menten_fitting()
        self.step6_machine_learning_classification()
        self.step7_model_explainability()
        self.step8_hierarchical_clustering()
        self.step9_anomaly_detection()
        
        # Save consolidated results
        self.save_results_to_csv()
        
        print("\n" + "=" * 60)
        print("ANALYSIS COMPLETE")
        print(f"All visualizations saved to: {self.output_dir}/visualizations/")
        print(f"Numerical results saved to: /Users/<USER>/Documents/nie/analysis_results.csv")
        print("=" * 60)

def main():
    """Main execution function."""
    analyzer = EnzymeKineticsAnalyzer("/Users/<USER>/Documents/nie/sample0513.csv")
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
