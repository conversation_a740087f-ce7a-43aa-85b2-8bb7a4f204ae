#!/usr/bin/env python3
"""
Enzyme Kinetics Analysis Launcher
================================

Interactive launcher for modular enzyme kinetics analysis.
Allows users to select specific analysis modules and configure parameters.
"""

import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.base import AnalysisConfig, DataManager, PlotManager, AnalysisResults
from modules.descriptive_stats import DescriptiveStatsModule, KineticCurveModule
from modules.quality_control import QualityControlModule
from modules.manova_analysis import MANOVAAnalysisModule
from modules.kinetic_modeling import KineticModelingModule
from modules.machine_learning import MachineLearningModule
from modules.explainability import ExplainabilityModule
from modules.clustering import ClusteringModule
from modules.anomaly_detection import AnomalyDetectionModule


class EnzymeKineticsLauncher:
    """Interactive launcher for enzyme kinetics analysis modules."""
    
    def __init__(self):
        self.config = AnalysisConfig()
        self.data_manager = None
        self.plot_manager = None
        self.results = AnalysisResults()
        self.available_modules = self._initialize_modules()
        
    def _initialize_modules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available analysis modules."""
        return {
            '1': {
                'name': '数据加载与初步探索',
                'description': '数据加载、清理、描述性统计和分布可视化',
                'class': DescriptiveStatsModule,
                'dependencies': []
            },
            '2': {
                'name': '动力学曲线可视化',
                'description': '浓度-响应关系分析，生成TMB和OPD动力学曲线',
                'class': KineticCurveModule,
                'dependencies': ['1']
            },
            '3': {
                'name': '数据质量控制 (QC)',
                'description': 'CV%计算、ICC分析、相关性分析和异常值检测',
                'class': QualityControlModule,
                'dependencies': ['1']
            },
            '4': {
                'name': '推断分析 (MANOVA)',
                'description': '多元方差分析，检验样本和浓度的交互效应',
                'class': MANOVAAnalysisModule,
                'dependencies': ['1']
            },
            '5': {
                'name': '米氏动力学方程拟合',
                'description': 'Michaelis-Menten参数估计 (Vmax, Km) 和置信区间',
                'class': KineticModelingModule,
                'dependencies': ['1', '2']
            },
            '6': {
                'name': '机器学习分类',
                'description': '梯度提升和1D-CNN模型训练，样本分类和性能评估',
                'class': MachineLearningModule,
                'dependencies': ['1', '5']
            },
            '7': {
                'name': '模型可解释性 (XAI)',
                'description': 'SHAP分析和t-SNE降维，特征重要性和聚类可视化',
                'class': ExplainabilityModule,
                'dependencies': ['6']
            },
            '8': {
                'name': '结果整合与聚类分析',
                'description': '基于动力学参数的层次聚类和样本相似性分析',
                'class': ClusteringModule,
                'dependencies': ['5', '7']
            },
            '9': {
                'name': '异常检测',
                'description': '隔离森林算法检测数据异常值和离群点',
                'class': AnomalyDetectionModule,
                'dependencies': ['1']
            }
        }
    
    def print_welcome(self):
        """Print welcome message and system info."""
        print("=" * 70)
        print("🧬 酶动力学数据分析系统 - 模块化版本")
        print("=" * 70)
        print("✨ 特色功能:")
        print("  • 模块化设计，独立运行不同分析步骤")
        print("  • 灵活的参数配置")
        print("  • 高质量科学可视化")
        print("  • 结构化结果导出")
        print("=" * 70)
        print(f"📊 当前数据文件: {self.config.get('data_file')}")
        print(f"📁 输出目录: {self.config.get('output_base_dir')}")
        print("=" * 70)
    
    def print_module_menu(self):
        """Print available modules menu."""
        print("\n🔬 可用分析模块:")
        print("-" * 50)
        
        for key, module_info in self.available_modules.items():
            status = "✅" if self._is_module_completed(key) else "⏸️"
            deps = ", ".join(module_info['dependencies']) if module_info['dependencies'] else "无"
            
            print(f"{status} [{key}] {module_info['name']}")
            print(f"    📝 {module_info['description']}")
            print(f"    🔗 依赖模块: {deps}")
            print()
        
        print("🛠️  其他选项:")
        print("  [c] 配置参数")
        print("  [s] 查看结果摘要")
        print("  [a] 运行所有模块")
        print("  [q] 退出程序")
        print("-" * 50)
    
    def _is_module_completed(self, module_key: str) -> bool:
        """Check if a module has been completed."""
        module_name = self.available_modules[module_key]['name']
        return module_name in self.results.results

    def _check_dependencies(self, module_key: str) -> bool:
        """Check if module dependencies are satisfied - more lenient approach."""
        dependencies = self.available_modules[module_key]['dependencies']
        
        # For critical dependencies, check if they completed successfully
        critical_deps = ['1']  # Module 1 is critical for data loading
        
        for dep in dependencies:
            if dep in critical_deps and not self._is_module_completed(dep):
                return False
            # For non-critical dependencies, just warn but allow execution
            elif dep not in critical_deps and not self._is_module_completed(dep):
                print(f"⚠️ 警告: 依赖模块 {dep} 未完成，但将尝试继续运行...")
        
        return True
    
    def configure_parameters(self):
        """Enhanced interactive parameter configuration."""
        print("\n⚙️ 参数配置系统")
        print("=" * 50)
        
        while True:
            print("\n配置选项:")
            print("  [1] 全局参数配置")
            print("  [2] 模块特定参数配置") 
            print("  [3] 保存配置到文件")
            print("  [4] 从文件加载配置")
            print("  [5] 查看当前配置")
            print("  [0] 返回主菜单")
            
            choice = input("\n请选择 [0-5]: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self._configure_global_parameters()
            elif choice == '2':
                self._configure_module_parameters()
            elif choice == '3':
                self._save_configuration()
            elif choice == '4':
                self._load_configuration()
            elif choice == '5':
                self._show_current_configuration()
            else:
                print("❌ 无效选择！")
    
    def _configure_global_parameters(self):
        """Configure global parameters."""
        print("\n🔧 全局参数配置")
        print("-" * 30)
        
        global_options = {
            '1': ('data_file', '数据文件路径', 'str'),
            '2': ('confidence_level', '置信水平', 'float'),
            '3': ('random_state', '随机种子', 'int'),
            '4': ('figure_dpi', '图像DPI', 'int'),
            '5': ('output_base_dir', '输出目录', 'str'),
        }
        
        print("当前全局配置:")
        for key, (param, desc, _) in global_options.items():
            current_value = self.config.get(param)
            print(f"  [{key}] {desc}: {current_value}")
        
        choice = input("\n请选择要修改的参数 [1-5] (直接回车跳过): ").strip()
        
        if choice in global_options:
            param, desc, param_type = global_options[choice]
            current_value = self.config.get(param)
            
            new_value = input(f"输入新的{desc} (当前: {current_value}): ").strip()
            
            if new_value:
                try:
                    if param_type == 'int':
                        new_value = int(new_value)
                    elif param_type == 'float':
                        new_value = float(new_value)
                    
                    self.config.update(**{param: new_value})
                    print(f"✅ {desc}已更新为: {new_value}")
                    
                    # Reinitialize managers if data file changed
                    if param == 'data_file':
                        self._initialize_managers()
                        
                except ValueError:
                    print(f"❌ 无效的{param_type}值")
    
    def _configure_module_parameters(self):
        """Configure module-specific parameters."""
        print("\n🔧 模块参数配置")
        print("-" * 30)
        
        modules = self.config.get_all_module_configs()
        module_list = list(modules.keys())
        
        print("可配置的模块:")
        for i, module in enumerate(module_list, 1):
            print(f"  [{i}] {module}")
        
        choice = input(f"\n请选择模块 [1-{len(module_list)}] (直接回车跳过): ").strip()
        
        try:
            if choice and 1 <= int(choice) <= len(module_list):
                selected_module = module_list[int(choice) - 1]
                self._configure_specific_module(selected_module)
        except ValueError:
            print("❌ 无效选择！")
    
    def _configure_specific_module(self, module_name: str):
        """Configure parameters for a specific module."""
        print(f"\n🔧 配置模块: {module_name}")
        print("-" * 40)
        
        current_config = self.config.get_module_config(module_name)
        
        if not current_config:
            print("该模块没有可配置参数。")
            return
        
        print("当前配置:")
        param_list = list(current_config.items())
        for i, (param, value) in enumerate(param_list, 1):
            print(f"  [{i}] {param}: {value}")
        
        choice = input(f"\n请选择要修改的参数 [1-{len(param_list)}] (直接回车跳过): ").strip()
        
        try:
            if choice and 1 <= int(choice) <= len(param_list):
                param_name, current_value = param_list[int(choice) - 1]
                
                # Determine parameter type
                param_type = type(current_value).__name__
                
                new_value = input(f"输入新的{param_name} (当前: {current_value}): ").strip()
                
                if new_value:
                    # Convert to appropriate type
                    if param_type == 'int':
                        new_value = int(new_value)
                    elif param_type == 'float':
                        new_value = float(new_value)
                    elif param_type == 'bool':
                        new_value = new_value.lower() in ['true', 'yes', '1', 'on']
                    
                    self.config.update_module_config(module_name, **{param_name: new_value})
                    print(f"✅ {param_name}已更新为: {new_value}")
                    
        except (ValueError, IndexError):
            print("❌ 无效选择或值！")
    
    def _save_configuration(self):
        """Save current configuration to file."""
        print("\n💾 保存配置")
        print("-" * 20)
        
        filename = input("输入配置文件名 (默认: enzyme_config.json): ").strip()
        if not filename:
            filename = "enzyme_config.json"
        
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = f"/Users/<USER>/Documents/nie/{filename}"
        
        try:
            self.config.save_config(filepath)
            print(f"✅ 配置已保存到: {filepath}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def _load_configuration(self):
        """Load configuration from file."""
        print("\n📂 加载配置")
        print("-" * 20)
        
        filename = input("输入配置文件名: ").strip()
        if not filename:
            print("❌ 未指定文件名")
            return
        
        if not filename.endswith('.json'):
            filename += '.json'
        
        filepath = f"/Users/<USER>/Documents/nie/{filename}"
        
        if self.config.load_config(filepath):
            print(f"✅ 配置已从 {filepath} 加载")
            # Reinitialize managers
            self._initialize_managers()
        else:
            print(f"❌ 加载失败")
    
    def _show_current_configuration(self):
        """Display current configuration."""
        print("\n📋 当前配置")
        print("=" * 50)
        
        print("\n🌐 全局配置:")
        for key, value in self.config.config.items():
            print(f"  {key}: {value}")
        
        print("\n🔧 模块配置:")
        for module_name, module_config in self.config.get_all_module_configs().items():
            print(f"\n  📦 {module_name}:")
            for param, value in module_config.items():
                print(f"    {param}: {value}")
    
    def _initialize_managers(self):
        """Initialize data and plot managers."""
        self.data_manager = DataManager(self.config)
        self.plot_manager = PlotManager(self.data_manager)
    
    def run_module(self, module_key: str) -> bool:
        """Run a specific analysis module with better error handling."""
        if module_key not in self.available_modules:
            print(f"❌ 无效的模块选择: {module_key}")
            return False
        
        module_info = self.available_modules[module_key]
        
        # Check critical dependencies only
        critical_deps = ['1']  # Only module 1 is truly critical
        missing_critical = [dep for dep in module_info['dependencies'] 
                           if dep in critical_deps and not self._is_module_completed(dep)]
        
        if missing_critical:
            print(f"❌ 缺少关键依赖模块: {', '.join(missing_critical)}")
            print("请先运行关键依赖模块！")
            return False
        
        # Initialize managers if not done
        if self.data_manager is None:
            self._initialize_managers()
        
        try:
            print(f"\n🚀 启动模块: {module_info['name']}")
            print("=" * 50)
            
            # Instantiate and run module
            module_class = module_info['class']
            module_instance = module_class(self.data_manager, self.plot_manager)
            
            start_time = datetime.now()
            result = module_instance.run_analysis()
            end_time = datetime.now()
            
            # Store results even if partial
            self.results.add_module_result(module_info['name'], result)
            
            duration = (end_time - start_time).total_seconds()
            print(f"✅ 模块完成！用时: {duration:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 模块运行失败: {str(e)}")
            print("尝试继续运行其他模块...")
            
            # Store partial result to indicate attempt was made
            self.results.add_module_result(module_info['name'], {'error': str(e), 'status': 'failed'})
            
            import traceback
            traceback.print_exc()
            return False
    
    def run_all_modules(self):
        """Run all available modules with improved error handling."""
        print("\n🔄 运行所有分析模块")
        print("=" * 50)
        
        # Sort modules by dependencies (simple topological sort)
        module_order = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        total_modules = len(module_order)
        completed = 0
        failed = 0
        
        for module_key in module_order:
            print(f"\n{'='*60}")
            print(f"正在处理模块 {module_key}/{total_modules}")
            print(f"{'='*60}")
            
            if self.run_module(module_key):
                completed += 1
                print(f"✅ 模块 {module_key} 成功完成")
            else:
                failed += 1
                print(f"❌ 模块 {module_key} 运行失败")
            
            print(f"📊 当前进度: 成功 {completed}, 失败 {failed}, 剩余 {total_modules - completed - failed}")
        
        print(f"\n🎉 批量运行完成！")
        print(f"📈 最终统计: 成功 {completed}/{total_modules}, 失败 {failed}/{total_modules}")
        
        # Save consolidated results regardless of failures
        if self.data_manager and self.data_manager.results:
            self.data_manager.save_results_to_csv()
    
    def show_results_summary(self):
        """Show summary of completed analyses."""
        print("\n📋 分析结果摘要")
        print("=" * 50)
        
        if not self.results.results:
            print("尚未运行任何分析模块。")
            return
        
        for module_name, result in self.results.results.items():
            print(f"✅ {module_name}")
            
            # Show key metrics if available
            if isinstance(result, dict):
                for key, value in result.items():
                    if isinstance(value, (int, float, str)) and len(str(value)) < 50:
                        print(f"    {key}: {value}")
                    elif isinstance(value, dict) and len(value) < 5:
                        print(f"    {key}: {len(value)} 项")
            print()
        
        # Show data manager results if available
        if self.data_manager and self.data_manager.results:
            print(f"📊 数值结果记录: {len(self.data_manager.results)} 条")
            print(f"📁 输出目录: {self.data_manager.output_dir}")
    
    def run_interactive_mode(self):
        """Run the interactive launcher."""
        self.print_welcome()
        
        while True:
            self.print_module_menu()
            
            choice = input("\n请选择操作: ").strip().lower()
            
            if choice == 'q':
                print("\n👋 感谢使用酶动力学分析系统！")
                break
            elif choice == 'c':
                self.configure_parameters()
            elif choice == 's':
                self.show_results_summary()
            elif choice == 'a':
                self.run_all_modules()
            elif choice in self.available_modules:
                self.run_module(choice)
            else:
                print("❌ 无效选择，请重试！")
            
            input("\n按回车键继续...")


def main():
    """Main entry point."""
    launcher = EnzymeKineticsLauncher()
    
    # Check if command line arguments provided for non-interactive mode
    if len(sys.argv) > 1:
        module_key = sys.argv[1]
        launcher._initialize_managers()
        launcher.run_module(module_key)
    else:
        launcher.run_interactive_mode()


if __name__ == "__main__":
    main()
