import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import curve_fit
from scipy.stats import sem, t
from statsmodels.multivariate.manova import MANOVA
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.manifold import TSNE
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# --- Configuration ---
FILE_PATH = 'sample0513.csv'
OUTPUT_CSV = 'analysis_results.csv'
PLOT_DPI = 300

# --- Main Analysis Function ---
def perform_full_analysis(file_path):
    """
    Main function to execute the 9-step data analysis pipeline.
    """
    results_dfs = [] # To store DataFrames from each step

    # --- Step 1: Data Loading and Exploration ---
    print("Step 1: Data Loading and Exploration...")
    df = pd.read_csv(file_path)
    df['concentration_val'] = df['concentration'].str.replace(' μM', '').astype(float)

    desc_stats = df[['TMB', 'OPD']].describe().reset_index().rename(columns={'index': 'statistic'})
    desc_stats['analysis_step'] = 'descriptive_stats'
    results_dfs.append(desc_stats)

    # Visualization: Boxplot (Corrected)
    plt.figure(figsize=(12, 7))
    sns.boxplot(data=df, x='sample', y='TMB', palette='viridis', hue='sample', dodge=False)
    plt.title('TMB Reading Distribution by Sample')
    plt.xlabel('Sample')
    plt.ylabel('TMB Reading')
    plt.legend([],[], frameon=False)
    plt.tight_layout()
    plt.savefig('boxplot_TMB_distribution.png', dpi=PLOT_DPI)
    plt.close()
    
    plt.figure(figsize=(12, 7))
    sns.boxplot(data=df, x='sample', y='OPD', palette='plasma', hue='sample', dodge=False)
    plt.title('OPD Reading Distribution by Sample')
    plt.xlabel('Sample')
    plt.ylabel('OPD Reading')
    plt.legend([],[], frameon=False)
    plt.tight_layout()
    plt.savefig('boxplot_OPD_distribution.png', dpi=PLOT_DPI)
    plt.close()
    print("Step 1 Complete.")

    # --- Step 2: Kinetic Curve Visualization ---
    print("Step 2: Kinetic Curve Visualization...")
    grouped = df.groupby(['sample', 'concentration_val'])
    kinetic_data = grouped[['TMB', 'OPD']].agg(['mean', 'std', 'count']).reset_index()
    kinetic_data.columns = ['_'.join(col).strip() for col in kinetic_data.columns.values]
    
    confidence = 0.95
    for substrate in ['TMB', 'OPD']:
        mean_col = f'{substrate}_mean'
        std_col = f'{substrate}_std'
        count_col = f'{substrate}_count'
        se = kinetic_data[std_col] / np.sqrt(kinetic_data[count_col])
        h = se * t.ppf((1 + confidence) / 2., kinetic_data[count_col]-1)
        kinetic_data[f'{substrate}_ci_lower'] = kinetic_data[mean_col] - h
        kinetic_data[f'{substrate}_ci_upper'] = kinetic_data[mean_col] + h

    kinetic_output = kinetic_data.copy()
    kinetic_output['analysis_step'] = 'kinetic_curve_data'
    results_dfs.append(kinetic_output)

    def plot_kinetic_curve(data, substrate):
        plt.figure(figsize=(12, 8))
        palette = sns.color_palette("viridis", len(data['sample_'].unique()))
        for i, (sample_id, group_data) in enumerate(data.groupby('sample_')):
            plt.plot(group_data['concentration_val_'], group_data[f'{substrate}_mean'], marker='o', color=palette[i], label=sample_id)
            plt.fill_between(group_data['concentration_val_'], group_data[f'{substrate}_ci_lower'], group_data[f'{substrate}_ci_upper'], color=palette[i], alpha=0.2)
        plt.xscale('log')
        plt.xlabel('Concentration (μM, log scale)')
        plt.ylabel(f'{substrate} Reading')
        plt.title(f'{substrate} Kinetic Curve by Sample with 95% CI')
        plt.legend(title='Sample')
        plt.grid(True, which="both", ls="--")
        plt.tight_layout()
        plt.savefig(f'kinetic_curves_{substrate}.png', dpi=PLOT_DPI)
        plt.close()

    plot_kinetic_curve(kinetic_data, 'TMB')
    plot_kinetic_curve(kinetic_data, 'OPD')
    print("Step 2 Complete.")
    
    # --- Step 3: Data Quality Control ---
    print("Step 3: Quality Control...")
    qc_data = kinetic_data.copy()
    for substrate in ['TMB', 'OPD']:
        qc_data[f'{substrate}_cv_percent'] = (qc_data[f'{substrate}_std'] / qc_data[f'{substrate}_mean']) * 100

    correlation = df[['TMB', 'OPD']].corr(method='pearson')
    corr_val = correlation.loc['TMB', 'OPD']
    
    plt.figure(figsize=(8, 8))
    sns.regplot(data=df, x='TMB', y='OPD', line_kws={"color": "red"})
    plt.title(f'TMB vs. OPD Correlation (Pearson: {corr_val:.3f})')
    plt.xlabel('TMB Reading')
    plt.ylabel('OPD Reading')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('correlation_TMB_vs_OPD.png', dpi=PLOT_DPI)
    plt.close()
    
    cv_heatmap_tmb = qc_data.pivot(index='sample_', columns='concentration_val_', values='TMB_cv_percent')
    cv_heatmap_opd = qc_data.pivot(index='sample_', columns='concentration_val_', values='OPD_cv_percent')
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    sns.heatmap(cv_heatmap_tmb, annot=True, fmt=".2f", cmap="viridis", linewidths=.5, ax=ax1, cbar_kws={'label': 'CV (%)'})
    ax1.set_title('TMB Coefficient of Variation (CV%) Heatmap')
    ax1.set_xlabel('Concentration (μM)')
    ax1.set_ylabel('Sample')
    
    sns.heatmap(cv_heatmap_opd, annot=True, fmt=".2f", cmap="plasma", linewidths=.5, ax=ax2, cbar_kws={'label': 'CV (%)'})
    ax2.set_title('OPD Coefficient of Variation (CV%) Heatmap')
    ax2.set_xlabel('Concentration (μM)')
    ax2.set_ylabel('Sample')
    
    plt.tight_layout()
    plt.savefig('cv_heatmap.png', dpi=PLOT_DPI)
    plt.close()
    
    qc_output = qc_data[['sample_', 'concentration_val_', 'TMB_cv_percent', 'OPD_cv_percent']].copy()
    qc_output['analysis_step'] = 'quality_control'
    corr_df = pd.DataFrame([{'analysis_step': 'quality_control', 'parameter': 'pearson_corr_TMB_OPD', 'value': corr_val}])
    results_dfs.append(qc_output)
    results_dfs.append(corr_df)
    print("Step 3 Complete.")

    # --- Step 4: MANOVA ---
    print("Step 4: MANOVA...")
    df['log_concentration'] = np.log1p(df['concentration_val'])
    manova = MANOVA.from_formula('TMB + OPD ~ C(sample) * concentration_val', data=df)
    manova_results = manova.mv_test()
    
    manova_df = manova_results.results['C(sample):concentration_val']['stat'].copy()
    manova_df['factor'] = manova_df.index
    manova_df['analysis_step'] = 'manova_results'
    results_dfs.append(manova_df)

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))
    sns.lineplot(data=df, x='concentration_val', y='TMB', hue='sample', ax=ax1, errorbar=None, palette='viridis')
    ax1.set_title('Interaction Effect: TMB')
    ax1.set_xscale('log')
    ax1.set_xlabel('Concentration (μM, log scale)')
    ax1.set_ylabel('TMB Reading')
    
    sns.lineplot(data=df, x='concentration_val', y='OPD', hue='sample', ax=ax2, errorbar=None, palette='plasma')
    ax2.set_title('Interaction Effect: OPD')
    ax2.set_xscale('log')
    ax2.set_xlabel('Concentration (μM, log scale)')
    ax2.set_ylabel('OPD Reading')
    
    plt.suptitle('Sample-Concentration Interaction Effects on TMB and OPD')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig('manova_interaction_plot.png', dpi=PLOT_DPI)
    plt.close()
    print("Step 4 Complete.")

    # --- Step 5: Michaelis-Menten Kinetics Fitting ---
    print("Step 5: Michaelis-Menten Fitting...")
    def michaelis_menten(S, Vmax, Km):
        return (Vmax * S) / (Km + S)

    mm_params = []
    unique_samples = sorted(df['sample'].unique())
    fig, axes = plt.subplots(int(np.ceil(len(unique_samples)/2)), 2, figsize=(15, 5*int(np.ceil(len(unique_samples)/2))), sharex=True)
    axes = axes.ravel()
    
    for i, sample_id in enumerate(unique_samples):
        sample_data = df[df['sample'] == sample_id]
        ax = axes[i]
        plot_colors = {'TMB': 'blue', 'OPD': 'red'}
        
        for substrate in ['TMB', 'OPD']:
            x_data = sample_data['concentration_val']
            y_data = sample_data[substrate]
            
            try:
                initial_guess = [y_data.max(), np.median(x_data)]
                params, covariance = curve_fit(michaelis_menten, x_data, y_data, p0=initial_guess, maxfev=5000)
                Vmax, Km = params
                
                std_errs = np.sqrt(np.diag(covariance))
                Vmax_ci = [Vmax - 1.96 * std_errs[0], Vmax + 1.96 * std_errs[0]]
                Km_ci = [Km - 1.96 * std_errs[1], Km + 1.96 * std_errs[1]]
                
                mm_params.append({
                    'analysis_step': 'michaelis_menten_params', 'sample': sample_id, 'substrate': substrate,
                    'Vmax': Vmax, 'Km': Km, 'Vmax_ci_lower': Vmax_ci[0], 'Vmax_ci_upper': Vmax_ci[1],
                    'Km_ci_lower': Km_ci[0], 'Km_ci_upper': Km_ci[1]
                })
                x_fit = np.logspace(np.log10(max(x_data.min(), 1e-3)), np.log10(x_data.max()), 100)
                y_fit = michaelis_menten(x_fit, Vmax, Km)
                ax.scatter(x_data, y_data, color=plot_colors[substrate], alpha=0.3)
                ax.plot(x_fit, y_fit, color=plot_colors[substrate], label=f'{substrate} Fit (Vmax={Vmax:.2f}, Km={Km:.2f})')
            except (RuntimeError, ValueError):
                print(f"Could not fit M-M curve for Sample {sample_id}, Substrate {substrate}")

        ax.set_xscale('log')
        ax.set_title(f'Sample {sample_id} - Michaelis-Menten Fit')
        ax.set_xlabel('Concentration (μM, log scale)')
        ax.set_ylabel('Reading')
        ax.legend()
        ax.grid(True, which="both", ls="--")

    plt.tight_layout()
    plt.savefig('michaelis_menten_fits.png', dpi=PLOT_DPI)
    plt.close()
    
    mm_df = pd.DataFrame(mm_params)
    results_dfs.append(mm_df)
    print("Step 5 Complete.")

    # --- Step 6: Machine Learning Classification (Random Forest) ---
    print("Step 6: Machine Learning Classification (using RandomForest)...")
    ml_df = df.copy()
    ml_df['TMB_OPD_ratio'] = ml_df['TMB'] / ml_df['OPD'].replace(0, 1e-9)
    ml_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    ml_df.fillna(0, inplace=True)
    
    features = ['concentration_val', 'log_concentration', 'TMB', 'OPD', 'TMB_OPD_ratio']
    X = ml_df[features]
    
    le = LabelEncoder()
    y = le.fit_transform(ml_df['sample'])
    
    rfc = RandomForestClassifier(random_state=42, n_estimators=100)
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    y_preds = np.zeros(len(X))
    for train_idx, val_idx in cv.split(X, y):
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train = y[train_idx]
        rfc.fit(X_train, y_train)
        y_preds[val_idx] = rfc.predict(X_val)

    accuracy = accuracy_score(y, y_preds)
    f1 = f1_score(y, y_preds, average='macro')
    cm = confusion_matrix(y, y_preds)
    
    ml_report = pd.DataFrame([{'analysis_step': 'ml_classification_report', 'model': 'RandomForest', 'accuracy': accuracy, 'f1_score_macro': f1}])
    results_dfs.append(ml_report)
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=le.classes_, yticklabels=le.classes_)
    plt.title('RandomForest Classifier Confusion Matrix')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.savefig('confusion_matrix_RandomForest.png', dpi=PLOT_DPI)
    plt.close()
    print("Step 6 Complete.")
    
    # --- Step 7: Model Interpretation (Feature Importance & t-SNE) ---
    print("Step 7: Model Interpretation...")
    rfc_final = RandomForestClassifier(random_state=42, n_estimators=100)
    rfc_final.fit(X, y)
    importances = rfc_final.feature_importances_
    
    feature_importance_df = pd.DataFrame({
        'feature': features,
        'importance': importances,
        'analysis_step': 'feature_importance'
    }).sort_values(by='importance', ascending=False)
    results_dfs.append(feature_importance_df)
    
    plt.figure(figsize=(10, 6))
    sns.barplot(x='importance', y='feature', data=feature_importance_df, palette='viridis', hue='feature', dodge=False)
    plt.title('RandomForest Feature Importance')
    plt.xlabel('Importance Score')
    plt.ylabel('Feature')
    plt.legend([],[], frameon=False)
    plt.tight_layout()
    plt.savefig('feature_importance_plot.png', dpi=PLOT_DPI)
    plt.close()

    tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X)-1), n_iter=1000)
    X_tsne = tsne.fit_transform(X)
    
    plt.figure(figsize=(10, 8))
    sns.scatterplot(x=X_tsne[:, 0], y=X_tsne[:, 1], hue=ml_df['sample'], palette='viridis', s=50)
    plt.title('t-SNE Visualization of Samples based on Features')
    plt.xlabel('t-SNE Component 1')
    plt.ylabel('t-SNE Component 2')
    plt.legend(title='Sample')
    plt.grid(True)
    plt.savefig('tsne_visualization.png', dpi=PLOT_DPI)
    plt.close()
    print("Step 7 Complete.")

    # --- Step 8: Clustering Analysis ---
    print("Step 8: Clustering...")
    if not mm_df.empty:
        cluster_features = mm_df.pivot(index='sample', columns='substrate', values=['Vmax', 'Km']).reset_index()
        cluster_features.columns = ['_'.join(col).strip('_') for col in cluster_features.columns.values]
        cluster_data = cluster_features.set_index('sample').fillna(0)

        plt.figure(figsize=(10, 7))
        g = sns.clustermap(cluster_data, cmap='viridis', standard_scale=1, annot=True)
        g.fig.suptitle('Hierarchical Clustering of Samples based on Kinetic Parameters (Vmax, Km)')
        plt.savefig('clustermap_summary.png', dpi=PLOT_DPI)
        plt.close()

        Z = linkage(cluster_data, method='ward')
        plt.figure(figsize=(10, 7))
        dendrogram(Z, labels=cluster_data.index)
        plt.title('Hierarchical Clustering Dendrogram')
        plt.xlabel('Sample')
        plt.ylabel('Distance')
        plt.savefig('hierarchical_clustering_dendrogram.png', dpi=PLOT_DPI)
        plt.close()
        
        cluster_labels = fcluster(Z, t=3, criterion='maxclust')
        cluster_df = pd.DataFrame({'analysis_step': 'clustering_results', 'sample': cluster_data.index, 'cluster_label': cluster_labels})
        results_dfs.append(cluster_df)
    else:
        print("Skipping clustering as Michaelis-Menten fitting was not successful for all samples.")
    print("Step 8 Complete.")

    # --- Step 9: Anomaly Detection ---
    print("Step 9: Anomaly Detection...")
    iso_forest = IsolationForest(contamination='auto', random_state=42)
    anomaly_features = df[['concentration_val', 'TMB', 'OPD']].fillna(0)
    anomaly_preds = iso_forest.fit_predict(anomaly_features)
    df['anomaly_score'] = iso_forest.decision_function(anomaly_features)
    df['anomaly_label'] = np.where(anomaly_preds == -1, 'Anomaly', 'Normal')

    anomaly_df = df[['concentration', 'sample', 'Parallel', 'anomaly_score', 'anomaly_label']].copy()
    anomaly_df['analysis_step'] = 'anomaly_detection_scores'
    results_dfs.append(anomaly_df)
    
    plt.figure(figsize=(12, 8))
    sns.scatterplot(data=df, x='concentration_val', y='TMB', hue='anomaly_label', style='anomaly_label', s=100, palette={'Normal': 'blue', 'Anomaly': 'red'})
    plt.xscale('log')
    plt.title('Anomaly Detection using Isolation Forest (TMB vs. Concentration)')
    plt.xlabel('Concentration (μM, log scale)')
    plt.ylabel('TMB Reading')
    plt.grid(True, which="both", ls="--")
    plt.savefig('anomaly_detection_plot.png', dpi=PLOT_DPI)
    plt.close()
    print("Step 9 Complete.")

    # --- Final Step: Consolidate and Save Results ---
    print("Consolidating and saving all results...")
    final_df = pd.concat(results_dfs, ignore_index=True)
    final_df.to_csv(OUTPUT_CSV, index=False)
    print(f"Analysis complete. All visualizations saved and results compiled in '{OUTPUT_CSV}'.")

# --- Execute the Analysis ---
if __name__ == '__main__':
    perform_full_analysis(FILE_PATH)